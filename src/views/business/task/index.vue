<template>
  <div class="table-box">
    <SearchForm
      v-show="isShowSearch && props.isSearch"
      @search="_search"
      @reset="_reset"
      :columns="searchColumns"
      :search-param="searchParam"
      :search-col="searchCol"
    />
    <div class="bg-white card table-main">
      <div
        class="content_box"
        v-loading="loading"
        element-loading-text="loading..."
        style="position: relative"
        element-loading-spinner="el-icon-loading"
        element-loading-background="rgba(0, 0, 0, 0.8)"
      >
        <!-- 列表展示部分开始 -->
        <div
          style="
            z-index: 2;
            display: flex;
            justify-content: flex-end;
            align-items: center;
            position: absolute;
            top: 10px;
            right: 20px;
          "
        >
          <el-button type="primary" @click="createdTask(1)" v-show="props.isSearch">新建任务</el-button>
        </div>

        <div class="task-detail-tabs-filter">
              <el-checkbox-group v-model="checkboxGroup1" size="default" @change="handleCheckboxChange">
                <el-checkbox
                  v-for="option in checkboxOptions"
                  :disabled="option.count ? false : true"
                  class="task-detail-tabs-filter-item"
                  :key="option.value"
                  :label="option.value"
                >
                  {{ option.label }}
                </el-checkbox>
              </el-checkbox-group>
            </div>
            <el-table :data="tableData" style="width: 100%;height: calc(100% - 100px);">
              <el-table-column prop="date" label="任务信息" width="240px">
                <template #default="scope">
                  <div>{{ scope.row.task_name }}</div>
                  <div class="task-detail-tabs-content">
                    <div>任务ID：{{ scope.row.id }}</div>
                    <div>创建时间：{{ scope.row.created_at }}</div>
                    <div class="flex align-center">
                      <el-tag class="mr-2" v-if="shouldShowTaskType(scope.row)" type="success" size="small">{{
                        getTaskTypeLabel(scope.row)
                      }}</el-tag>
                      <el-tag size="small">{{ platform_type_text[scope.row.platform_type] }}</el-tag>
                    </div>
                  </div>
                </template>
              </el-table-column>
              <el-table-column prop="date" label="项目信息" width="220px">
                <template #default="scope">
                  <div>{{ scope?.row?.project?.project_name }}</div>
                  <div class="task-detail-tabs-content">项目ID：{{ scope?.row?.project_id }}</div>
                  <div class="task-detail-tabs-content">创建时间：{{ scope?.row?.created_at }}</div>
                </template>
              </el-table-column>
              
           
              <!-- <el-table-column prop="date" label="考核要求" width="150px">
                <template #default="scope">
                  <el-tooltip placement="bottom" effect="light">
                    <template #content>
                      <div class="p-1">
                        <div>CPM:{{ scope?.row?.project?.cpm_min_price }}-{{ scope?.row?.project?.cpm_max_price }}</div>
                        <div class="mt-2">
                          CPE:{{ scope?.row?.project?.cpe_min_price }}-{{ scope?.row?.project?.cpe_max_price }}
                        </div>
                      </div>
                    </template>
                    <div class="cursor-pointer flex align-center p-2" style="display: flex; align-items: center">
                      CPM:{{ scope?.row?.project?.cpm_min_price }}-{{ scope?.row?.project?.cpm_max_price
                      }}<el-icon style="transform: rotate(90deg)">
                        <MoreFilled />
                      </el-icon>
                    </div>
                  </el-tooltip>
                </template>
              </el-table-column> -->
              <el-table-column prop="total_predict_receivable_customer_price" label="任务预估金额" width="120">
                <template #default="scope">
                  ¥ {{ (scope?.row?.total_predict_receivable_customer_price || 0)?.toLocaleString() }}
                </template>
              </el-table-column>
              <el-table-column prop="date" label="总毛利率" width="100px">
                <template #default="scope">
                  <div>{{ (scope.row.total_gross ? scope.row.total_gross  : 0).toFixed(2) + "%" }}</div>
                </template>
              </el-table-column>
              <el-table-column prop="date" label="客户信息" width="240px">
                <template #default="scope">
                  <div>{{ scope?.row?.project?.custom_name }}</div>
                </template>
              </el-table-column>
              <el-table-column prop="name" label="达人信息" width="200px">
                <template #default="scope">
                  <div v-if="scope?.row?.process_kol?.length > 1" class="avatar-stack">
                    <el-avatar
                      v-for="item in scope?.row?.process_kol.filter((v, i) => i <= 2)"
                      :key="item.platform_uid"
                      class="avatar"
                      shape="circle"
                      size="small"
                      :src="item?.kol_photo"
                    ></el-avatar>
                    <div v-show="scope?.row?.process_kol?.length > 3" class="number-avatar">
                      +{{ scope?.row?.process_kol.length - 3 }}
                    </div>
                  </div>
                  <div v-else-if="scope?.row?.process_kol?.length == 1" class="avatar-stack">
                    <div
                      v-for="item in scope?.row?.process_kol"
                      style="display: flex; align-items: center"
                      :key="item?.platform_uid"
                    >
                      <div style="width: 28px; height: 28px">
                        <el-avatar class="avatar" shape="circle" size="small" :src="item?.kol_photo"></el-avatar>
                      </div>
                      <p>{{ item?.kol_name }}</p>
                    </div>
                  </div>
                  <div v-else class="avatar-stack">
                    <div class="avatar-stack-name">无</div>
                  </div>
                </template>
              </el-table-column>
              <el-table-column prop="amount" label="参与人员" width="130">
                <template #default="scope">
                  <el-tooltip placement="bottom" effect="light">
                    <template #content>
                      <template v-if="scope?.row?.status === 2">
                        <p>信息填写人员：{{ scope?.row?.participants?.information_personnel || "-" }}</p>
                      </template>
                      <template v-else-if="scope?.row?.status === 5 || scope?.row?.status === 3">
                        <p>信息填写人员：{{ scope?.row.participants?.information_personnel || "-" }}</p>
                        <!-- 按照审核级别分组显示审核人员 -->
                        <template v-if="scope?.row?.reviewers && scope?.row?.reviewers.length">
                          <template v-for="level in [...new Set(scope?.row?.reviewers.map(r => r.reviewer_level))].sort()">
                            <p>{{ getLevelText(level) }}：
                              <template v-if="typeof formatReviewerNames(scope?.row?.reviewers, level) === 'string'">
                                {{ formatReviewerNames(scope?.row?.reviewers, level) }}
                              </template>
                              <template v-else>
                                <span class="reviewer-names">{{ formatReviewerNames(scope?.row?.reviewers, level).displayNames }}</span>
                                <template v-if="formatReviewerNames(scope?.row?.reviewers, level).hasMore">
                                  <span v-if="!isExpanded(scope?.row?.id, level)" 
                                        class="show-more" 
                                        @click.stop="toggleExpand(scope?.row?.id, level)">
                                    (共{{ formatReviewerNames(scope?.row?.reviewers, level).totalCount }}人，点击展开)
                                  </span>
                                  <template v-else>
                                    <div class="reviewer-list">
                                      {{ formatReviewerNames(scope?.row?.reviewers, level).nameList.join('、') }}
                                    </div>
                                    <span class="show-less" @click.stop="toggleExpand(scope?.row?.id, level)">
                                      (收起)
                                    </span>
                                  </template>
                                </template>
                              </template>
                            </p>
                          </template>
                        </template>
                      </template>
                      <template v-else-if="scope?.row?.status === 4">
                        <p>信息填写人员：{{ scope?.row.participants?.information_personnel || "-" }}</p>
                        <!-- 按照审核级别分组显示审核人员 -->
                        <template v-if="scope?.row?.reviewers && scope?.row?.reviewers.length">
                          <template v-for="level in [...new Set(scope?.row?.reviewers.map(r => r.reviewer_level))].sort()">
                            <p>{{ getLevelText(level) }}：
                              <template v-if="typeof formatReviewerNames(scope?.row?.reviewers, level) === 'string'">
                                {{ formatReviewerNames(scope?.row?.reviewers, level) }}
                              </template>
                              <template v-else>
                                <span class="reviewer-names">{{ formatReviewerNames(scope?.row?.reviewers, level).displayNames }}</span>
                                <template v-if="formatReviewerNames(scope?.row?.reviewers, level).hasMore">
                                  <span v-if="!isExpanded(scope?.row?.id, level)" 
                                        class="show-more" 
                                        @click.stop="toggleExpand(scope?.row?.id, level)">
                                    (共{{ formatReviewerNames(scope?.row?.reviewers, level).totalCount }}人，点击展开)
                                  </span>
                                  <template v-else>
                                    <div class="reviewer-list">
                                      {{ formatReviewerNames(scope?.row?.reviewers, level).nameList.join('、') }}
                                    </div>
                                    <span class="show-less" @click.stop="toggleExpand(scope?.row?.id, level)">
                                      (收起)
                                    </span>
                                  </template>
                                </template>
                              </template>
                            </p>
                          </template>
                        </template>
                      </template>
                      <template v-else-if="scope?.row?.status === 6">
                        <p>信息填写人员：{{ scope?.row.participants?.information_personnel || "-" }}</p>
                        <!-- 按照审核级别分组显示审核人员 -->
                        <template v-if="scope?.row?.reviewers && scope?.row?.reviewers.length">
                          <template v-for="level in [...new Set(scope?.row?.reviewers.map(r => r.reviewer_level))].sort()">
                            <p>{{ getLevelText(level) }}：
                              <template v-if="typeof formatReviewerNames(scope?.row?.reviewers, level) === 'string'">
                                {{ formatReviewerNames(scope?.row?.reviewers, level) }}
                              </template>
                              <template v-else>
                                <span class="reviewer-names">{{ formatReviewerNames(scope?.row?.reviewers, level).displayNames }}</span>
                                <template v-if="formatReviewerNames(scope?.row?.reviewers, level).hasMore">
                                  <span v-if="!isExpanded(scope?.row?.id, level)" 
                                        class="show-more" 
                                        @click.stop="toggleExpand(scope?.row?.id, level)">
                                    (共{{ formatReviewerNames(scope?.row?.reviewers, level).totalCount }}人，点击展开)
                                  </span>
                                  <template v-else>
                                    <div class="reviewer-list">
                                      {{ formatReviewerNames(scope?.row?.reviewers, level).nameList.join('、') }}
                                    </div>
                                    <span class="show-less" @click.stop="toggleExpand(scope?.row?.id, level)">
                                      (收起)
                                    </span>
                                  </template>
                                </template>
                              </template>
                            </p>
                          </template>
                        </template>
                        <p v-show="scope?.row?.order_created_name">下单人员：{{ scope?.row?.order_created_name || "-" }}</p>
                      </template>
                      <template v-else-if="scope?.row?.status === 7">
                        <p>信息填写人员：{{ scope?.row.participants?.information_personnel || "-" }}</p>
                        <!-- 按照审核级别分组显示审核人员 -->
                        <template v-if="scope?.row?.reviewers && scope?.row?.reviewers.length">
                          <template v-for="level in [...new Set(scope?.row?.reviewers.map(r => r.reviewer_level))].sort()">
                            <p>{{ getLevelText(level) }}：
                              <template v-if="typeof formatReviewerNames(scope?.row?.reviewers, level) === 'string'">
                                {{ formatReviewerNames(scope?.row?.reviewers, level) }}
                              </template>
                              <template v-else>
                                <span class="reviewer-names">{{ formatReviewerNames(scope?.row?.reviewers, level).displayNames }}</span>
                                <template v-if="formatReviewerNames(scope?.row?.reviewers, level).hasMore">
                                  <span v-if="!isExpanded(scope?.row?.id, level)" 
                                        class="show-more" 
                                        @click.stop="toggleExpand(scope?.row?.id, level)">
                                    (共{{ formatReviewerNames(scope?.row?.reviewers, level).totalCount }}人，点击展开)
                                  </span>
                                  <template v-else>
                                    <div class="reviewer-list">
                                      {{ formatReviewerNames(scope?.row?.reviewers, level).nameList.join('、') }}
                                    </div>
                                    <span class="show-less" @click.stop="toggleExpand(scope?.row?.id, level)">
                                      (收起)
                                    </span>
                                  </template>
                                </template>
                              </template>
                            </p>
                          </template>
                        </template>
                        <p v-show="scope?.row?.order_created_name">下单人员：{{ scope?.row?.order_created_name || "-" }}</p>
                      </template>
                    </template>
                    <p class="cursor-pointer flex align-center" style="display: flex; align-items: center">
                      <span v-show="scope?.row?.status === 0">{{ scope?.row?.created_name }}</span>
                      <span v-show="scope?.row?.status === 1">{{ scope?.row?.participants.alliance_personnel }}</span>
                      <span v-show="scope?.row?.status === 2">{{ scope?.row?.participants.information_personnel }}</span>
                      
                      <!-- 处理状态3或5的情况，显示多个审核人员的汇总 -->
                      <span v-show="scope?.row?.status === 3 || scope?.row?.status == 5">
                        <template v-if="(scope?.row?.reviewers || []).length > 1">
                          {{ findReviewerByLevel(scope?.row?.reviewers, scope?.row?.level)?.reviewer_personnel || "-" }}
                        </template>
                      </span>
                      
                      <!-- 处理状态4的情况，显示多个审核人员的汇总 -->
                      <span v-show="scope?.row?.status === 4">
                        <template v-if="(scope?.row?.reviewers || []).length > 1">
                          {{ findReviewerByLevel(scope?.row?.reviewers, scope?.row?.reviewers?.[0]?.reviewer_level)?.reviewer_personnel || "-" }}
                        </template>
                      </span>
                      
                      <span v-show="scope?.row?.status === 6 || scope?.row?.status === 7">{{
                        scope?.row?.participants?.information_personnel
                      }}</span>
                      <el-icon style="transform: rotate(90deg)">
                        <MoreFilled />
                      </el-icon>
                    </p>
                  </el-tooltip>
                </template>
              </el-table-column>
              <!-- 结算方式列 - 仅在抖音平台指派任务模式下显示 -->
              <el-table-column prop="settlement_method" label="结算方式" width="120" v-if="shouldShowSettlementMethodColumn">
                <template #default="scope">
                  <div v-if="shouldShowSettlementMethodForRow(scope.row)">
                    {{ getSettlementMethodLabel(scope.row.settlement_method) }}
                  </div>
                  <div v-else>-</div>
                </template>
              </el-table-column>
              <el-table-column prop="stats" label="任务状态" align="center" width="140">
                <template #default="scope">
                  <div style="display: flex; flex-direction: column; align-items: center" v-if="scope?.row?.status == 4">
                    {{ status[scope?.row?.status] }}
                    <p class="cursor-pointer color-blue500" @click="checkCause(scope?.row)">查看拒绝原因</p>
                  </div>
                  <div v-else>
                    {{ status[scope?.row?.status] }}
                  </div>
                </template>
              </el-table-column>
              <el-table-column label="操作" min-width="180px" fixed="right" align="center">
                <template #default="scope">
                  <el-button
                    type="text"
                    :disabled="!scope.row?.is_owner_status"
                    v-show="scope.row?.status == 0 && scope.row?.is_owner_status"
                    class="cursor-pointer color-blue500"
                    @click="goToOrderPath('selectAuthor', scope?.row)"
                    >去选号</el-button
                  >
                  <el-button
                    type="text"
                    v-show="scope.row?.is_update && scope.row?.status == 3"
                    class="cursor-pointer color-blue500 ml-2"
                    @click="handleEdit(scope?.row)"
                    >修改</el-button
                  >
                  <el-button
                    type="text"
                    :disabled="!scope.row?.is_owner_status"
                    v-show="!(scope.row?.status >= 5 || scope.row?.status == 3) && scope.row?.is_owner_status"
                    class="cursor-pointer color-blue500 ml-2"
                    @click="edit(scope?.row)"
                    >编辑</el-button
                  >
                  <el-button
                    type="text"
                    :disabled="!scope.row?.is_owner_status"
                    v-show="scope.row?.status == 2 && scope.row?.is_owner_status"
                    class="cursor-pointer color-blue500"
                    @click="goToOrderPath('informationAuthor', scope?.row)"
                    >去填写</el-button
                  >
                  <el-button
                    type="text"
                    :disabled="!scope.row?.is_update"
                    v-show="scope.row?.status == 4 && scope.row?.is_update"
                    class="cursor-pointer color-blue500"
                    @click="goToOrderPath('informationAuthor', scope?.row)"
                    >去修改</el-button
                  >
                  <el-button
                    type="text"
                    :disabled="!scope.row?.is_owner_status"
                    v-show="(scope.row?.status == 3 || scope.row?.status == 5) && scope.row?.is_owner_status"
                    class="cursor-pointer color-blue500"
                    @click="goToOrderPath('examineAuthor', scope?.row)"
                    >去审核</el-button
                  >
                  <el-button
                    type="text"
                    :disabled="!scope.row?.is_owner_status || scope.row?.order_method != 1"
                    v-show="scope.row?.status == 6 && scope.row?.is_owner_status"
                    class="cursor-pointer color-blue500"
                    @click="goToOrderPath('createOrder', scope?.row)"
                    >去下单</el-button>
                  <el-button
                    type="text"
                    v-show="isDouyinRecruitmentTask(scope?.row) && scope.row?.status !== 2 && scope.row?.change_kol_permission"
                    class="cursor-pointer color-blue500 ml-2"
                    @click="goToTaskChange(scope?.row)"
                    >达人变更</el-button
                  >
                  <el-button type="text" class="cursor-pointer color-blue500 ml-2" @click="goToTaskOrderDetail(scope?.row)"
                    >详情</el-button
                  >
                  <el-button
                    type="text"
                    v-show="scope.row?.created_id == user_id && (scope.row?.status == 3 || scope.row?.status == 5)"
                    class="cursor-pointer color-blue500 ml-2"
                    @click="expediting(scope?.row)"
                    >催办</el-button
                  >
                  <el-button
                    type="text"
                    v-show="scope.row?.withdraw_task_permission"
                    :disabled="!scope.row?.withdraw_task_permission"
                    class="cursor-pointer color-blue500"
                    @click="cancelTask(scope?.row)"
                    >撤回</el-button
                  >
                  <el-button
                    type="text"
                    :disabled="!scope.row?.delete_task_permission"
                    v-show="scope.row?.delete_task_permission"
                    class="cursor-pointer color-blue500"
                    @click="deleteTask(scope?.row)"
                    >删除</el-button
                  >
                </template>
              </el-table-column>
            </el-table>
            <div class="flex justify-end" style="margin-bottom: 10px">
              <el-pagination
                v-model:current-page="formInline.page"
                v-model:page-size="formInline.page_size"
                :total="query.total"
                :page-sizes="[10, 20, 30, 50]"
                @size-change="handleSizeChangeOder"
                @current-change="handleCurrentChangeOder"
                background
                layout="total ,sizes, prev, pager, next, jumper"
              />
            </div>
        <!-- 列表展示部分结束 -->
      </div>
    </div>
    <el-dialog v-model="dialogOrderVisible" title="下单信息">
      <el-table
        :data="gridOrderData"
        :cell-style="{ textAlign: 'center' }"
        :header-cell-style="{ textAlign: 'center', backgroundColor: '#ffffff', color: '#606266' }"
      >
        <!-- 复选框列 -->
        <el-table-column width="55">
          <template #default="scope">
            <el-checkbox
              v-model="selectedOrders[scope.$index]"
              v-show="scope.row?.order_status === 0"
              @change="changeOrderInfo($event)"
            ></el-checkbox>
          </template>
        </el-table-column>

        <!-- 达人名称 -->
        <el-table-column prop="kol_name" label="达人名称" width="200">
          <template #default="scope">
            <div class="search-div-photo flex items-center">
              <img v-if="scope.row?.kol_photo" :src="scope.row?.kol_photo" width="45" height="45" alt="" />
              <img src="../../../assets/img/default-face.png" v-else width="45" height="45" alt="" />
              <span class="ml-4 font-semibold">{{ scope.row?.kol_name }}</span>
            </div>
            ID:{{ scope.row?.platform_uid }}
          </template>
        </el-table-column>

        <!-- 服务类型 -->
        <el-table-column prop="name" label="服务类型" :width="zoom > 150 ? 200 : null">
          <template #default="scope">
            <el-select style="width: 120px" disabled v-model="scope.row.cooperation_type">
              <el-option v-for="i in placementListArr" :key="i.value" :value="i.value" :label="i.label" />
            </el-select>
          </template>
        </el-table-column>

        <!-- 数量 -->
        <el-table-column prop="name" label="数量" :width="zoom > 150 ? 140 : null">
          <template #default="scope">
            <el-input-number style="width: 100px" v-model="scope.row.kol_num" disabled />
          </template>
        </el-table-column>

        <!-- 价格 -->
        <el-table-column prop="name" label="价格" width="150">
          <template #default="scope">
            <div>¥{{ scope.row?.kol_price }}</div>
          </template>
        </el-table-column>

        <!-- 状态 -->
        <el-table-column prop="name" label="状态" width="90">
          <template #default="scope">
            <div>{{ scope.row?.order_status === 0 ? "未下单" : "已下单" }}</div>
          </template>
        </el-table-column>
      </el-table>

      <template #footer>
        <div style="display: flex; justify-content: space-between; margin-top: 20px; padding-left: 20px; color: #666">
          <!-- 全选复选框 -->
          <el-checkbox v-model="selectAll" @change="toggleSelectAll">&nbsp;全选未下单达人</el-checkbox>
          <el-button type="primary" @click="confirmOrder()">确定下单</el-button>
        </div>
      </template>
    </el-dialog>
    <el-drawer destroy-on-close	 :title="'任务管理' + ' / ' +(projectDrawerType == 0 ? '新建任务' : '编辑任务')" v-model="projectAddDrawer" size="1200px">
      <div class="drawer-container">
        <el-form
          :model="form"
          :rules="rule"
          ref="ruleForm"
          label-width="120px"
          class="form-style"
        >
          <div class="form-container">
            <el-row :gutter="20">

              <el-col :span="12">
                <el-form-item label="所属项目" prop="project_id" required>
                  <el-select
                    v-model="form.project_id"
                    filterable
                    remote
                    reserve-keyword
                    placeholder="请选择项目"
                    :remote-method="remoteMethod"
                    :loading="loading2"
                    style="width: 100%"
                  >
                    <el-option 
                      v-for="item in options" 
                      :key="item.custom_id" 
                      :label="formatProjectName(item)" 
                      :value="item.id" 
                    />
                  </el-select>
                </el-form-item>
              </el-col>
              
              <el-col :span="12">
                <el-form-item label="媒体平台" required>
                  <el-input
                    v-model="platformLabel"
                    placeholder="根据所属项目自动带出"
                    disabled
                    style="width: 100%"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="12" v-if="form.project_id && [1, 2, 5].includes(Number(form.platform_type))">
                <el-form-item label="任务模式" prop="task_type" required>
                  <el-select
                    v-model="form.task_type"
                    placeholder="请选择任务模式"
                    style="width: 100%"
                  >
                    <el-option
                      v-for="item in getTaskModeOptions()"
                      :key="item.value"
                      :label="item.label"
                      :value="item.value"
                      :disabled="item.disabled"
                    />
                  </el-select>
                </el-form-item>
              </el-col>
              <!-- 结算方式字段 - 仅在抖音平台指派任务模式下显示 -->
              <el-col :span="12" v-if="shouldShowSettlementMethod()">
                <el-form-item label="结算方式" prop="settlement_method" required>
                  <el-select
                    v-model="form.settlement_method"
                    placeholder="请选择结算方式"
                    style="width: 100%"
                  >
                    <el-option label="一口价" :value="1"></el-option>
                    <el-option label="按照自然播放量结算" :value="3"></el-option>
                    <el-option label="按转化结算" :value="4"></el-option>
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item prop="order_method" required>
                  <template #label>
                    <div class="form-item-label-with-icon">
                      <span>下单方式</span>
                      <el-tooltip
                        content="'抖音-星立方'仅支持星推下单；'抖音-其他'平台的招募和投稿任务仅支持线下下单；其他平台仅支持集采线下下单"
                        placement="top"
                        effect="light"
                      >
                        <el-icon class="question-icon"><QuestionFilled /></el-icon>
                      </el-tooltip>
                    </div>
                  </template>
                  <el-select
                    v-model="form.order_method"
                    placeholder="请选择下单方式"
                    style="width: 100%"
                  >
                    <el-option 
                      v-for="item in filteredOrderMethodOptions" 
                      :key="item.value" 
                      :label="item.label" 
                      :value="item.value"
                      :disabled="item.disabled"
                    />
                  </el-select>
                </el-form-item>
              </el-col>
              
              <el-col :span="12">
                <el-form-item label="任务名称" prop="task_name" required>
                  <el-input v-model="form.task_name" placeholder="请输入任务名称" style="width: 100%" />
                </el-form-item>
              </el-col>
              
              <el-col :span="12">
                <el-form-item label="客户主体" prop="customer_entity" required>
                  <div class="form-input-with-action">
                    <el-input v-model="form.customer_entity" placeholder="根据所属项目自动带出" disabled style="width: 100%" />
                    <div class="form-action">
                      <el-button type="primary" link size="small" @click="openEditCustomerEntityDialog">修改</el-button>
                    </div>
                  </div>
                </el-form-item>
              </el-col>
              
              <el-col :span="12">
                <el-form-item label="客户合同" prop="contract_id" required>
                  <el-select
                    v-model="form.contract_id"
                    placeholder="请搜索合同名称或合同编号"
                    filterable
                    remote
                    reserve-keyword
                    :remote-method="searchContract"
                    @change="contract_id_change"
                    :loading="contractLoading"
                    style="width: 100%"
                  >
                    <el-option
                      v-for="item in customerUscList"
                      :key="item.contract_id"
                      :label="item.contract_id + ' - ' + item.contract_name + (item.contract_amount ? ' (¥' + item.contract_amount + ')' : '')"
                      :value="item.contract_id"
                    >
                        <div class="contract-option">
                          <div class="contract-option-title"><b>{{ item.contract_id }}</b> - {{ item.contract_name }}{{ item.contract_amount ? ' (¥' + item.contract_amount + ')' : '' }}</div>
                          <div class="contract-option-id">合同编号: {{ item.contract_id }}</div>
                          <div class="contract-option-project">
                            项目名称: {{ extractProjectName(item) || '无项目名称' }}
                            <span class="contract-option-note">(将用于OA项目)</span>
                          </div>
                        </div>
                    </el-option>
                  </el-select>
                  
                  <div v-if="form.contract_id && isCurrentObj.value" class="contract-details">
                    <div class="contract-details-item">
                      <span class="contract-label">合同名称:</span> 
                      <span>{{ isCurrentObj.value.contract_name || '--' }}</span>
                      <el-tag size="small" type="success">已保存至客户合同字段</el-tag>
                    </div>
                    <div class="contract-details-item">
                      <span class="contract-label">合同编号:</span> 
                      <span>{{ isCurrentObj.value.contract_id || '--' }}</span>
                    </div>
                    <div class="contract-details-item contract-dates">
                      <span class="contract-label">合同签订日期:</span> 
                      <span>{{ isCurrentObj.value.contract_sign_date || '--' }}</span>
                    </div>
                    <div class="contract-details-item contract-dates">
                      <span class="contract-label">合同有效期:</span> 
                      <span>从 {{ isCurrentObj.value.contract_start_date || '--' }}</span>
                      <span>至 <strong class="expire-date">{{ isCurrentObj.value.contract_end_date || '--' }}</strong></span>
                    </div>
                    <div v-if="isCurrentObj.value.project_name" class="contract-details-item">
                      <span class="contract-label">项目名称:</span>
                      <span>{{ isCurrentObj.value.project_name }}</span>
                      <el-tag size="small" type="warning">自动填充到OA项目</el-tag>
                    </div>
                    <div v-else-if="isCurrentObj.value.oa_project_name" class="contract-details-item">
                      <span class="contract-label">OA项目:</span>
                      <span>{{ isCurrentObj.value.oa_project_name }}</span>
                      <el-tag size="small" type="warning">自动填充到OA项目</el-tag>
                    </div>
                  </div>
                </el-form-item>
              </el-col>
              
              <el-col :span="12">
                <el-form-item label="所属OA项目" prop="oa_project_id" required>
                    <el-input
                      v-model="form.oa_project_id"
                      placeholder="选择客户合同后自动填充"
                      disabled
                      style="width: 100%"
                    />
                </el-form-item>
              </el-col>
              
              <el-col :span="12">
                <el-form-item label="业绩归属人" prop="performance_owner" required>
                  <el-input
                    v-model="form.performance_owner"
                    placeholder="选择客户合同后自动填充"
                    disabled
                    style="width: 100%"
                  />
                </el-form-item>
              </el-col>
              
              <el-col :span="12">
                <el-form-item label="业绩归属部门" prop="performance_department" required>
                  <el-input 
                    v-model="form.performance_department" 
                    placeholder="选择客户合同后自动填充" 
                    disabled 
                    style="width: 100%" 
                  />
                </el-form-item>
              </el-col>
              
              <el-col :span="12">
                <el-form-item label="媒体平台主体" prop="platform_type_entity" required>
                  <el-input v-model="form.platform_type_entity" placeholder="固定主体信息" disabled style="width: 100%" />
                </el-form-item>
              </el-col>
              
             
              
              <el-col :span="12">
                <el-form-item label="订单类型" prop="order_type" required>
                  <el-select 
                    v-model="form.order_type" 
                    placeholder="请选择订单类型" 
                    style="width: 100%"
                  >
                    <el-option 
                      v-for="item in orderTypeOptions" 
                      :key="item.value" 
                      :label="item.label" 
                      :value="item.value" 
                    />
                  </el-select>
                </el-form-item>
              </el-col>
              
              

              <!-- 账户选择字段 -->
              <el-col :span="12">
                <el-form-item 
                  label="账户选择" 
                  prop="account_id" 
                >
                  <div class="form-input-with-action">
                    <el-tooltip
                      v-if="form.account_id"
                      placement="top"
                      effect="light"
                      :content="getAccountTooltipContent()"
                      :popper-style="{ maxWidth: '320px', padding: '10px' }"
                      raw-content
                    >
                      <el-input
                        v-model="form.account_name"
                        placeholder="请选择账户"
                        disabled
                        style="width: 100%"
                      />
                    </el-tooltip>
                    <el-input
                      v-else
                      v-model="form.account_name"
                      placeholder="请选择账户"
                      disabled
                      style="width: 100%"
                    />
                    <div class="form-action">
                      <el-button
                        type="primary"
                        @click="openAccountDialog"
                        :disabled="form.order_method !== '2'"
                      >选择账户</el-button>
                    </div>
                  </div>
                  
                  <div class="field-note" v-if="form.order_method === '2' && !form.account_id">
                    <el-tag size="small" type="danger">集采线下下单时必须选择账户</el-tag>
                  </div>
                </el-form-item>
              </el-col>

              <el-col :span="12">
                <el-form-item 
                  label="充值合同" 
                  prop="recharge_contract"
                >
                  <el-select
                    v-model="form.recharge_contract"
                    :placeholder="getRechargeContractPlaceholder()"
                    filterable
                    remote
                    reserve-keyword
                    :remote-method="searchRechargeContract"
                    :loading="rechargeContractLoading"
                    @focus="handleRechargeContractFocus"
                    @clear="clearRechargeContract" 
                    clearable
                    style="width: 100%"
                    :disabled="!isRechargeContractRequired() || !form.contract_id"
                    :key="`recharge-contract-${form.contract_id}`"
                    :filter-method="localFilterRechargeContracts"
                  >
                    <el-option v-for="item in filteredRechargeContracts" :key="item.value" :label="item.label" :value="item.value" />
                  </el-select>

                  <div class="field-note" v-if="isRechargeContractRequired() && !form.recharge_contract">
                    <el-tag size="small" type="warning">
                     当前媒体平台必须选择充值合同
                    </el-tag>
                  </div>
                </el-form-item>
              </el-col>
              
              <el-col :span="24">
                <el-form-item label="备注" prop="remark">
                  <el-input
                    v-model="form.remark"
                    type="textarea"
                    placeholder="请输入备注信息"
                    :rows="4"
                    style="width: 100%"
                  />
                </el-form-item>
              </el-col>
            </el-row>
          </div>
        </el-form>
      </div>
      
      <template #footer>
        <div class="drawer-footer">
          <el-button @click="closeDrawer">取消</el-button>
          <el-button type="primary" @click="onSubmit(ruleForm)">确认</el-button>
          <el-button type="primary" @click="onSubmitAndNext(ruleForm)">确认并下一步</el-button>
        </div>
      </template>
    </el-drawer>
    <el-dialog v-model="dialogTableVisible" title="拒绝原因" width="500px">
      <el-table :data="gridData">
        <el-table-column property="kols" label="达人" width="240px">
          <template #default="scope">
            <div style="display: flex; align-items: center; justify-content: space-between">
              <div style="width: 28px; height: 28px">
                <el-avatar class="avatar" shape="circle" size="small" :src="scope.row?.kols.kol_photo"></el-avatar>
              </div>
              <div style="flex: 1">
                <p>{{ scope.row?.kols.kol_name }}</p>
                <p style="font-size: 12px; color: #999">ID:{{ scope.row?.kols.platform_uid }}</p>
              </div>
            </div>
          </template>
        </el-table-column>
        <el-table-column property="reviewer_comments" label="拒绝原因" width="200" />
      </el-table>
      <template #footer>
        <div style="display: flex; justify-content: flex-end; margin-top: 20px">
          <el-button type="primary" @click="dialogTableVisible = false">确定</el-button>
        </div>
      </template>
    </el-dialog>
    <taskDrawer ref="drawerRef" />
    
    <!-- 新增客户主体修改弹窗 -->
    <el-dialog v-model="customerEntityDialogVisible" title="修改客户主体" width="500px">
      <el-form :model="customerEntityForm" label-width="100px">
        <el-form-item label="客户主体" required>
          <el-select
            v-model="customerEntityForm.customer_entity"
            filterable
            remote
            reserve-keyword
            placeholder="请搜索客户简称/全称/集团名称"
            :remote-method="customerEntityRemoteMethod"
            :loading="customerEntityLoading"
            style="width: 100%"
            @change="handleEntitySelectChange"
          >
            <el-option
              v-for="item in customerEntityOptions"
              :key="item.custom_id"
              :label="item.custom_name"
              :value="item.custom_name"
            />
          </el-select>
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="customerEntityDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="saveCustomerEntity">确认</el-button>
        </span>
      </template>
    </el-dialog>
    
    <!-- 新增账户选择弹窗 -->
    <el-dialog v-model="accountDialogVisible" title="选择账户" width="900px">
      <div class="account-search-form mb-4">
        <el-form :inline="true" :model="accountSearchForm">
          <el-form-item label="账户ID">
            <el-input 
              v-model="accountSearchForm.customer_account_id" 
              placeholder="请输入账户ID" 
              clearable
              @keyup.enter="searchAccounts"
            ></el-input>
          </el-form-item>
          <el-form-item label="账户名称">
            <el-input 
              v-model="accountSearchForm.account_name" 
              placeholder="请输入账户名称" 
              clearable
              @keyup.enter="searchAccounts"
            ></el-input>
          </el-form-item>
          <el-form-item label="客户名称">
            <el-input
              v-model="accountSearchForm.customer_name"
              placeholder="请输入客户名称"
              clearable
              @keyup.enter="searchAccounts"
            ></el-input>
          </el-form-item>
          <el-form-item label="媒体平台">
            <el-select
              v-model="accountSearchForm.media_platform"
              placeholder="请选择媒体平台"
              clearable
              style="width: 120px"
            >
              <el-option
                v-for="item in mediaPlatforms"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="searchAccounts">查询</el-button>
            <el-button @click="resetAccountSearch">重置</el-button>
          </el-form-item>
        </el-form>
      </div>
      
      <el-table
        ref="accountTable"
        :data="accountOptions"
        style="width: 100%"
        height="400px"
        v-loading="accountLoading"
        highlight-current-row
        @current-change="handleAccountRowChange"
      >
        <el-table-column width="50" align="center">
          <template #default="scope">
            <el-radio 
              v-model="selectedAccountId" 
              :label="scope.row.id"
              @change="() => handleRadioChange(scope.row)"
            >&nbsp;</el-radio>
          </template>
        </el-table-column>
        <el-table-column prop="company" label="所属公司" width="120"></el-table-column>
        <el-table-column prop="media_platform" label="媒体平台" width="80"></el-table-column>
        <el-table-column prop="customer_name" label="客户名称" width="120"></el-table-column>
        <el-table-column prop="customer_account_id" label="客户账户ID" min-width="160"></el-table-column>
        <el-table-column prop="account_name" label="账户名称" min-width="160"></el-table-column>
        <el-table-column prop="brand_name" label="品牌名称" min-width="160"></el-table-column>
        <!-- <el-table-column prop="customer_short_name" label="客户简称" min-width="160"></el-table-column> -->
      </el-table>
      
      <div class="pagination-container mt-4">
        <el-pagination
          v-model:current-page="accountPagination.currentPage"
          v-model:page-size="accountPagination.pageSize"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          :total="accountPagination.total"
          @size-change="handleAccountSizeChange"
          @current-change="handleAccountPageChange"
        ></el-pagination>
      </div>
      
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="accountDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="confirmSelectAccount">确认</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="tsx" name="businessProject">
import { ref, reactive, computed, onMounted, watch, onBeforeUnmount, onActivated, nextTick } from "vue";
import { useRouter, useRoute } from "vue-router";
import { Business } from "@/api/interface";
import SearchForm from "@/components/SearchForm/index.vue";
import { ElMessage, ElMessageBox, ElLoading } from "element-plus";
import { handleProp } from "@/utils";
import taskDrawer from "@/views/business/components/taskDrawer.vue";
import { ColumnProps } from "@/components/ProTable/interface";
import { useSocketStore } from "@/stores/socketIo/socketStore"; // 引入 Pinia store
import { QuestionFilled } from "@element-plus/icons-vue"; // 导入问号图标

import {
  searchTasks,
  addTasks,
  updateTasks,
  cancelTaskApi,
  searchProjects,
  deleteTasks,
  xingtuTaskOrderKolInfoApi,
  contractInformationList,
  getRechargeInfo,
  getRechargeContract,
  oaMediaListApi
} from "@/api/modules/business";
import { customerProfile } from "@/api/modules/order";
import { useTabsStore } from "@/stores/modules/tabs";

const ruleForm = ref(null);
const router = useRouter();
const route = useRoute();
const socketStore = useSocketStore();
const customerUscList = ref([]);
const tabStore = useTabsStore();

// 添加 isInitialFormLoad 变量声明
const isInitialFormLoad = ref(false);

// 定义一个函数将数字转换为对应的中文级别描述
const getLevelText = (level) => {
  const levelMap = {
    1: '一级审核人',
    2: '二级审核人',
    3: '三级审核人',
    4: '四级审核人',
    5: '五级审核人'
  };
  return levelMap[level] || `${level}级审核人`;
};

// 添加一个函数来格式化审核人员名单，处理大量审核人员的情况
const formatReviewerNames = (reviewers, level, maxDisplay = 5) => {
  if (!reviewers || !reviewers.length) return "-";
  
  // 筛选出指定级别的审核人员
  const filteredReviewers = reviewers
    .filter(r => r.reviewer_level === level)
    .map(r => r.reviewer_personnel)
    .filter(Boolean);
  
  if (!filteredReviewers.length) return "-";
  
  // 如果人员数量超过最大显示数量，显示部分并添加展开/收起按钮
  if (filteredReviewers.length > maxDisplay) {
    return {
      nameList: filteredReviewers,
      displayNames: filteredReviewers.slice(0, maxDisplay).join('、'),
      totalCount: filteredReviewers.length,
      hasMore: true
    };
  }
  
  // 否则直接显示所有人员
  return {
    nameList: filteredReviewers,
    displayNames: filteredReviewers.join('、'),
    totalCount: filteredReviewers.length,
    hasMore: false
  };
};

// 用于跟踪每个审核级别是否展开显示全部人员
const expandedLevels = ref({});

// 切换展开/收起状态的方法
const toggleExpand = (taskId, level) => {
  const key = `${taskId}-${level}`;
  expandedLevels.value[key] = !expandedLevels.value[key];
};

// 判断某个审核级别是否展开
const isExpanded = (taskId, level) => {
  const key = `${taskId}-${level}`;
  return !!expandedLevels.value[key];
};

// 是否显示搜索模块
const user_id = ref(localStorage.getItem("userId"));
const isShowSearch = ref(true);
const searchParam = ref({});
const searchCol = { xs: 1, sm: 2, md: 2, lg: 3, xl: 4 };
const activeName = ref("first");
const loading = ref(false);
const checkboxGroup1 = ref([]);
const checkboxGroup2 = ref([]);
const projectAddDrawer = ref(false);
const projectDrawerType = ref(0);
const dialogTableVisible = ref(false);
const gridData = ref([]);
const tableData = ref([]);
const checkboxOptions = ref([
  { label: `全部（0）`, value: "", count: 0 },
  { label: `待填写下单信息（0）`, value: "2", count: 0 },
  { label: `待审核（0）`, value: "3", count: 0 },
  { label: `审核拒绝（0）`, value: "4", count: 0 },
  { label: `待下单（0）`, value: "6", count: 0 },
  { label: `已完成（0）`, value: "7", count: 0 }
]);
const form = ref({
  project_id: "",
  task_name: "",
  promotion_platforms_genres: 1,
  // 统一使用字符串类型的task_type，移除重复定义
  task_type: "",
  settlement_method: 1,
  order_type: 1, // Default to 1 "自运营"
  oa_project_id: "",
  customer_entity: "",
  customer_contract: "",
  contract_id: "", // Added field to store contract ID separately
  usc_code: "", // 添加客户主体的USC代码字段，用于合同查询
  performance_owner: "",
  performance_department: "",
  platform_type: 1,
  platform_type_entity: "", // This should match the entity for platform_type 1
  order_method: "", // 统一使用字符串类型的order_method
  recharge_contract: "",
  remark: "", // Changed from remarks to remark to match API
  // 新增字段：账户ID和账户名称
  account_id: "",
  account_name: "",
  account_brand_name: "", // 添加账户品牌名称字段
  // Additional fields required by API
  customer_code: "",
  approval_type_text: "",
  contract_type: "",
  media_or_channel: "",
  contract_sign_date: "",
  contract_start_date: "",
  contract_end_date: "",
  contract_amount: "",
  contract_relative_name: "",
  company: "",
  oa_project_name: "",
  operator: "", // Same as performance_owner
  department: "" // Same as performance_department
});
const platform_type_text = ref({
  "1": "抖音",
  "2": "抖音星立方",
  "3": "小红书",
  "4": "快手",
  "5": "B站",
  "6": "腾讯互选"
});
const rule = reactive({
  task_name: [
    { required: true, message: "请输入任务名称", trigger: "blur" },
    { min: 1, max: 50, message: "长度在 1 到 50 字符", trigger: "blur" }
  ],
  project_id: [
    { required: true, message: "请选择所属项目", trigger: "change" }
  ],
  oa_project_id: [
    { required: true, message: "所属OA项目不能为空", trigger: "change" }
  ],
  contract_id: [
    { required: true, message: "请选择客户合同", trigger: "change" }
  ],
  // Customer entity is auto-populated from project data
  customer_entity: [
    { required: true, message: "客户主体不能为空，请选择所属项目", trigger: "change" }
  ],
  // Performance owner/department are auto-populated from contract data
  // but we still need validation rules for better error messages
  performance_owner: [
    { required: true, message: "业绩归属人不能为空，请选择客户合同", trigger: "change" }
  ],
  performance_department: [
    { required: true, message: "业绩归属部门不能为空，请选择客户合同", trigger: "change" }
  ],
  platform_type: [
    { required: true, message: "请选择媒体平台", trigger: "change" }
  ],
  order_method: [
    { required: true, message: "请选择下单方式", trigger: "change" }
  ],
  order_type: [
    { required: true, message: "请选择订单类型", trigger: "change" }
  ],
  // 账户ID验证规则 - 集采线下下单时必填
  account_id: [
    { 
      required: false, 
      validator: (rule, value, callback) => {
        if (form.value.order_method === '2' && !value) {
          callback(new Error("集采线下下单时请选择账户"));
        } else {
          callback();
        }
      }, 
      trigger: "change" 
    }
  ],
  // 添加充值合同验证规则
  recharge_contract: [
    {
      required: false,
      validator: (rule, value, callback) => {
        // 使用新的判断函数
        if (isRechargeContractRequired() && !value) {
          callback(new Error("该媒体平台类型需要选择充值合同"));
        } else {
          callback();
        }
      },
      trigger: "change"
    }
  ],
  // Add task mode validation rule
  task_type: [
    {
      required: false,
      validator: (rule, value, callback) => {
        const platformType = Number(form.value.platform_type);
        // Only required if project is selected and platform is Douyin (both types) or Bilibili
        if (form.value.project_id && [1, 2, 5].includes(platformType) && !value) {
          callback(new Error("请选择任务模式"));
        } else {
          callback();
        }
      },
      trigger: "change"
    }
  ],
  // Add settlement method validation rule
  settlement_method: [
    {
      required: false,
      validator: (rule, value, callback) => {
        // 只有在显示结算方式字段时才需要验证
        if (shouldShowSettlementMethod() && !value) {
          callback(new Error("请选择结算方式"));
        } else {
          callback();
        }
      },
      trigger: "change"
    }
  ]
});
const task_type = ref({
  1: "指派",
  2: "招募",
  3: "投稿",
});

// Add task mode options for different platforms
const taskModeOptions = ref({
  douyin: [ // For Douyin (platform_type: 1)
    { label: "指派", value: "1" },
    { label: "招募", value: "2" },
    { label: "投稿", value: "3" }
  ],
  bilibili: [ // For Bilibili (platform_type: 5)
    { label: "京火", value: "1" },
    { label: "花火", value: "2" }
  ]
});

const status = ref({
  2: "待填写下单信息",
  3: "待审核",
  4: "审核拒绝",
  5: "待审核",
  6: "审核通过待下单",
  7: "下单前流程结束"
});
const task_time = ref([]);

// 判断是否需要充值合同的函数
const isRechargeContractRequired = () => {
  const platformType = Number(form.value.platform_type);
  const selectedProject = options.value.find(project => project.id === form.value.project_id);

    // 抖音平台(1)且推广内容为星立方(promote_type === 1)需要充值合同
  if (platformType === 1 && selectedProject && selectedProject.promote_type === 1) {
    return true;
  }

  // 小红书(3)需要充值合同
  if (platformType === 3) {
    return true;
  }

  // B站(5)需要充值合同
  if (platformType === 5) {
    return true;
  }

  return false;
};

// 接收父组件通过props传值
const props = defineProps({
  projectId: {
    type: Number,
    default: 0
  },
  isSearch: {
    type: Boolean,
    default: true
  }
});

watch(
  socketStore.messages,
  () => {
    console.log(socketStore.messages, "全局连接接收到消息");
    let msg = socketStore.messages[socketStore.messages.length - 1];
    //接收消息通知
    if (msg.event == "error_message") {
      ElMessage.error(msg.data.msg);
    }
    if (msg.event == "success_message") {
      ElMessage.success(msg.data.msg);
    }
  },
  { deep: true }
);

const checkCause = value => {
  dialogTableVisible.value = true;
  gridData.value = value.last_rejected;
};

const handleCheckboxChange = e => {
  if (checkboxGroup1.value.length > 1) {
    checkboxGroup1.value = [checkboxGroup1.value[checkboxGroup1.value.length - 1]];
  }
  formInline.value.status = checkboxGroup1.value[0];
  getTaskList();
};

// 页面按钮权限（按钮权限既可以使用 hooks，也可以直接使用 v-auth 指令，指令适合直接绑定在按钮上，hooks 适合根据按钮权限显示不同的内容）
// const { BUTTONS } = useAuthButtons();
//分页 改变当前页个数
const handleSizeChangeOder = val => {
  formInline.value.page_size = val;
  getTaskList();
};
//分页 改变当前页
const handleCurrentChangeOder = val => {
  formInline.value.page = val;
  getTaskList();
};

const _search = (params?) => {
  if (activeName.value == "first") {
    if (params?.task_time) {
      formInline.value.task_create_time = params.task_time[0];
      formInline.value.task_end_time = params.task_time[1];
    }
    formInline.value = { ...formInline.value, ...params };
    getTaskList();
  }
};

const searchFormRef = ref(null);
const _reset = () => {
  if (activeName.value == "first") {
    formInline.value = {
      task_type: "",
      task_search: "",
      custom_name: "",
      project_name: "",
      project_id: "",
      status: "",
      order_status: "",
      created_id: "",
      created_name: "",
      task_create_time: "",
      task_end_time: "",
      page_size: 10,
      page: 1
    };
    // 确保清空搜索参数中的日期值
    searchParam.value.task_time = null;
    task_time.value = [];
    searchFormRef.value?.reset();
    getTaskList();
  } 
};
const options = ref([]);

const remoteMethodSelect = () => {
  loading2.value = true;
  let params = {
    search: "",
    page: 1,
    page_size: 10
  };
  searchProjects(params).then(res => {
    loading2.value = false;
    if (res && res.code === 990 && res.data && Array.isArray(res.data.lists)) {
      const searchDate = res.data.lists;
      options.value = searchDate;
    }
  });
};

const loading2 = ref(false);
const remoteMethod = query => {
  if (query) {
    loading2.value = true;
    let params = {
      project_name: query,
      page: 1,
      page_size: 10
    };
    searchProjects(params).then(res => {
      loading2.value = false;
      if (res && res.code === 990 && res.data && Array.isArray(res.data.lists)) {
        const searchDate = res.data.lists;
        options.value = searchDate;
      }
    });
  } else {
    remoteMethodSelect();
  }
};

//编辑
const edit = async row => {
  console.log(row, "row");
  projectAddDrawer.value = true;
  projectDrawerType.value = 1;
  
  // 设置初始加载标志为 true，防止 watcher 清空充值合同
  isInitialFormLoad.value = true;
  
  // Set loading state
  const loadingInstance = ElLoading.service({
    fullscreen: true,
    text: '加载任务数据中...',
    background: 'rgba(0, 0, 0, 0.7)'
  });
  
  try {
    remoteMethod(row.project_id);
    
    const customerCode = row.participants?.customer_code || row.customer_code || "";
    const rechargeContractId = row.recharge_contract_id || row.recharge_contract || "";
    
    console.log("Row data for contract loading:", {
      row_project: row.project,
      project_custom_name: row.project?.custom_name,
      usc_code: row.project?.usc_code,
      customer_code: customerCode,
      recharge_contract_id: rechargeContractId, // Updated log field name
      customer_code_source: row.participants?.customer_code ? "participants.customer_code" : "row.customer_code",
      task_type: row.task_type, // 记录原始任务类型值
      order_method: row.order_method, // 记录原始下单方式值
      platform_type: row.platform_type, // 记录原始平台类型
      platform_type_entity: row.platform_type_entity, // 记录原始平台主体
      account_id: row.account_id, // 记录账户ID
      account_name: row.account_name // 记录账户名称
    });
    
    // 暂存关键字段的数值，确保不会被后面的操作清空
    const platformType = Number(row.platform_type || 0);
    const taskType = Number(row.task_type || 0);
    const orderMethod = Number(row.order_method || 0);
    
    // 确定正确的平台主体
    let platformTypeEntity = row.platform_type_entity || "";
    
    // 如果为空或者不匹配当前平台，重新设置
    if (!platformTypeEntity || platformType === 5 && platformTypeEntity !== "上海东方传媒（集团）有限公司‌‌") {
      // 根据平台类型设置正确的平台主体
      switch(platformType) {
        case 1:
          platformTypeEntity = "武汉星图新视界科技有限公司";
          break;
        case 2:
          platformTypeEntity = "武汉星图新视界科技有限公司";
          break;
        case 3:
          platformTypeEntity = "薯鸿文化传媒（上海）有限公司";
          break;
        case 4:
          platformTypeEntity = "北京晨钟科技有限公司";
          break;
        case 5:
          platformTypeEntity = "上海东方传媒（集团）有限公司‌‌";
          break;
        case 6:
          platformTypeEntity = "深圳市腾讯文化传媒有限公司";
          break;
      }
    }
    
    console.log("Using platform entity:", platformTypeEntity);
    
    // Pre-set the form with available data
    form.value = {
      project_id: row.project_id,
      task_name: row.task_name,
      promotion_platforms_genres: 1,
      task_type: String(taskType || ""), // 转为字符串类型避免类型不匹配
      settlement_method: row.settlement_method || 1,
      order_type: Number(row.order_type || 1),
      oa_project_id: row.oa_project_id || "",
      customer_entity: row.project?.custom_name || row.customer_entity || "",
      customer_contract: row.customer_contract || "",
      contract_id: customerCode,
      usc_code: row.project?.usc_code || "", // 保存项目的USC代码到表单中
      performance_owner: row.performance_owner || "",
      performance_department: row.performance_department || "",
      platform_type: platformType,
      platform_type_entity: platformTypeEntity, // 使用确定的平台主体
      order_method: String(orderMethod || ""), // 转为字符串类型避免类型不匹配
      recharge_contract: rechargeContractId, // Set recharge_contract using recharge_contract_id
      remark: row.remark || "",
      // Add task mode from row data
      account_id: row.account_id || "",
      account_name: row.account_name || "",
      account_brand_name: row.account_brand_name || "", // 添加账户品牌名称
      customer_code: customerCode,
      approval_type_text: row.approval_type_text || "",
      contract_type: row.contract_type || "",
      media_or_channel: row.media_or_channel || "",
      contract_sign_date: row.contract_sign_date || "",
      contract_start_date: row.contract_start_date || "",
      contract_end_date: row.contract_end_date || "",
      contract_amount: row.contract_amount || "",
      contract_relative_name: row.contract_relative_name || "",
      company: row.company || "",
      oa_project_name: row.oa_project_name || "",
      operator: row.operator || "",
      department: row.department || ""
    };
    
    const platformOption = mediaPlatformOptions.value.find(item => item.value === platformType);
    platformLabel.value = platformOption ? platformOption.label : '';
    
    if (row.id) {
      form.value.task_id = row.id;
    }
    
    // 为了确保数据在项目和平台加载完成后正确设置，使用setTimeout
    setTimeout(() => {
      // 确保平台主体和平台类型匹配
      if (platformType === 5) {
        // 对B站平台特别确认平台主体
        form.value.platform_type_entity = "上海东方传媒（集团）有限公司‌‌";
      }
      
      console.log("手动设置关键字段:", {
        platform_type: form.value.platform_type,
        platform_type_entity: form.value.platform_type_entity,
        task_type: form.value.task_type,
        order_method: form.value.order_method
      });
    }, 500);
    
    if (row.account_id && (row.order_method === '2' || platformType === 5)) {
      form.value.account_id = row.account_id;
      form.value.account_name = row.account_name;
      form.value.account_brand_name = row.account_brand_name || ""; // 确保账户品牌名称也被加载
      
      // 对于B站平台，强制设置下单方式
      if (platformType === 5) {
        form.value.order_method = "2"; // B站平台使用集采线下下单
      }
    }
    
    if (rechargeContractId && customerCode) {
      console.log("Preloading recharge contract:", rechargeContractId);
      
      if (rechargeContractDebounceTimer) {
        clearTimeout(rechargeContractDebounceTimer);
        rechargeContractDebounceTimer = null;
      }
      lastRechargeContractRequest = 0;
      
      form.value.recharge_contract = rechargeContractId;
      
      loadRechargeContracts(customerCode, rechargeContractId, false);
      
      setTimeout(() => {
        if (!form.value.recharge_contract) {
          console.log("Reapplying recharge_contract value after API call:", rechargeContractId);
          form.value.recharge_contract = rechargeContractId;
        }
      }, 500);
    }
    
    if (row.project && row.project.usc_code) {
      await new Promise((resolve) => {
        contractLoading.value = true;
        contractInformationList({
          usc_code: row.project.usc_code,
          contract_id: customerCode || "",
          page: 1,
          page_size: 999
        }).then(res => {
          if (res && res.code === 990 && res.data) {
            const contractData = res.data;
            // Safely access the lists property with a type assertion
            const contractLists = Array.isArray((contractData as any).lists) ? (contractData as any).lists : [];
            
            console.log("Contract list loaded successfully:", contractLists.length, "contracts found");
            customerUscList.value = contractLists;
            
            // Find the matching contract
            const selectedContract = customerUscList.value.find(
              contract => contract.contract_id === customerCode
            );
            
            if (selectedContract) {
              console.log("Found matching contract:", selectedContract.contract_id);
              isCurrentObj.value = selectedContract;
              contract_id_change(customerCode);
            } else {
              console.log("No matching contract found for ID:", customerCode);
              // Use fallback data
              isCurrentObj.value = {
                contract_id: customerCode,
                contract_name: row.customer_contract || "",
                contract_sign_date: row.contract_sign_date || "",
                contract_start_date: row.contract_start_date || "",
                contract_end_date: row.contract_end_date || "",
                project_name: row.oa_project_name || ""
              };
            }
          } else {
            console.warn("Failed to load contracts or no contracts found");
            // Use fallback data
            isCurrentObj.value = {
              contract_id: customerCode,
              contract_name: row.customer_contract || "",
              contract_sign_date: row.contract_sign_date || "",
              contract_start_date: row.contract_start_date || "",
              contract_end_date: row.contract_end_date || "",
              project_name: row.oa_project_name || ""
            };
          }
          contractLoading.value = false;
          resolve(true);
        }).catch(err => {
          console.error("Error loading contracts:", err);
          contractLoading.value = false;
          resolve(true);  // Still resolve to continue
        });
      });
    } else {
      console.warn("Missing USC code from project data. Looking for project info in options.");
      
      // Wait a short time for project options to load
      await new Promise(resolve => setTimeout(resolve, 300));
      
      const selectedProject = options.value.find(project => project.id === row.project_id);
      if (selectedProject && selectedProject.usc_code) {
        console.log("Found project USC code from options:", selectedProject.usc_code);
        
        // Use another promise to handle this async load
        await new Promise((resolve) => {
          contractLoading.value = true;
          contractInformationList({
            usc_code: selectedProject.usc_code,
            contract_id: customerCode || "",
            page: 1,
            page_size: 999
          }).then(res => {
            if (res && res.code === 990 && res.data) {
              const contractData = res.data;
              // Safely access the lists property with a type assertion
              const contractLists = Array.isArray((contractData as any).lists) ? (contractData as any).lists : [];
              customerUscList.value = contractLists;
              
              const selectedContract = customerUscList.value.find(
                contract => contract.contract_id === customerCode
              );
              
              if (selectedContract) {
                isCurrentObj.value = selectedContract;
                contract_id_change(customerCode);
              }
            }
            contractLoading.value = false;
            resolve(true);
          }).catch(err => {
            console.error("Error loading contracts:", err);
            contractLoading.value = false;
            resolve(true);  // Still resolve to continue
          });
        });
        
        form.value.customer_entity = selectedProject.custom_name || row.project?.custom_name || row.customer_entity || "";
      } else {
        console.error("Could not find USC code for project. Contract information cannot be loaded.");
        ElMessage.warning("无法加载合同信息：缺少项目USC代码");
      }
    }
    
    // 在所有异步加载完成后，延迟关闭初始加载标志
    setTimeout(() => {
      // 确保充值合同值仍然正确
      if (rechargeContractId && !form.value.recharge_contract) {
        console.log("Final check: reapplying recharge contract value:", rechargeContractId);
        form.value.recharge_contract = rechargeContractId;
      }
      
      // 使用nextTick确保视图已更新后再关闭初始加载标志
      nextTick(() => {
        // 再次确认任务模式和下单方式值设置正确
        console.log("Final check for task_type and order_method:", {
          task_type: {
            original: taskType,
            current: form.value.task_type,
            matched: String(taskType) === form.value.task_type
          },
          order_method: {
            original: orderMethod,
            current: form.value.order_method,
            matched: String(orderMethod) === form.value.order_method
          },
          platform_type: {
            value: form.value.platform_type,
            entity: form.value.platform_type_entity
          }
        });
        
        // 如果值不匹配，再次设置
        if (taskType && form.value.task_type !== String(taskType)) {
          form.value.task_type = String(taskType);
        }
        
        if (orderMethod && form.value.order_method !== String(orderMethod)) {
          form.value.order_method = String(orderMethod);
        }
        
        // B站平台的特殊处理
        if (platformType === 5) {
          // 再次确保B站平台主体和下单方式正确
          form.value.platform_type_entity = "上海东方传媒（集团）有限公司‌‌";
          form.value.order_method = "2"; // B站平台使用集采线下下单
        }
        
        // 最后再关闭初始加载标志
        console.log("Initial form load complete, contract_id watcher now active");
        isInitialFormLoad.value = false;
      });
    }, 1500); // 给足够的时间让所有异步操作完成
  } catch (error) {
    console.error("Error in edit task function:", error);
    ElMessage.error("加载任务数据失败");
    isInitialFormLoad.value = false;
  } finally {
    loadingInstance.close();
  }
};

const onSubmit = async formEl => {
  if (!formEl) return;
  await formEl.validate((valid, fields) => {
    if (valid) {
      let data = JSON.parse(JSON.stringify(form.value));
      
      console.log("Form submission data:", {
        platform_type: data.platform_type,
        platform_type_entity: data.platform_type_entity,
        complete_data: data
      });
      
      // 如果task_type是空字符串，将其设置为0
      if (data.task_type === "") {
        console.log("Converting empty task_type to 0");
        data.task_type = 0;
      }
      
      let project_info = options.value.find(item => item.id === data.project_id);
      
      if (project_info) {
        data.project_name = project_info.project_name;
      } else {
        ElMessage.warning('项目信息获取失败');
        return;
      }
      
      if (!data.contract_id) {
        ElMessage.warning('请选择客户合同');
        return;
      }
      
      if (!data.customer_entity) {
        ElMessage.warning('客户主体不能为空，请选择所属项目');
        return;
      }
      
      data.customer_contract_id = data.contract_id;
      
      // Map the recharge_contract field to recharge_contract_id expected by the API
      if (data.recharge_contract) {
        data.recharge_contract_id = data.recharge_contract;
      }
      
      if (isCurrentObj.value) {
        data.contract_sign_date = isCurrentObj.value.contract_sign_date || data.contract_sign_date || "";
        data.contract_start_date = isCurrentObj.value.contract_start_date || data.contract_start_date || "";
        data.contract_end_date = isCurrentObj.value.contract_end_date || data.contract_end_date || "";
      }
      
      console.log("Submit data with contract dates:", {
        contract_sign_date: data.contract_sign_date,
        contract_start_date: data.contract_start_date,
        contract_end_date: data.contract_end_date,
        recharge_contract_id: data.recharge_contract_id // Log the mapped field
      });
      
      if (!data.performance_owner || !data.performance_department) {
        ElMessage.warning('业绩归属人和业绩归属部门不能为空，请选择包含这些信息的客户合同');
        return;
      }
      
      if (!data.order_method) {
        ElMessage.warning('请选择下单方式');
        return;
      }
      
      // 验证集采线下下单时必须选择账户
      if (data.order_method === 2 && !data.account_id) {
        ElMessage.warning('集采线下下单时必须选择账户');
        return;
      }
      
      // 确保集采线下下单时包含账户品牌名称
      if (data.order_method === 2 && data.account_id && !data.account_brand_name) {
        console.log('账户品牌名称为空，使用默认值');
      }
      
      data.operator = data.performance_owner;
      data.department = data.performance_department;
      
      data.platform_type = form.value.platform_type;
      data.platform_type_entity = form.value.platform_type_entity;
      
      data.platform_type = Number(data.platform_type);
      
      // 重命名充值合同字段，从recharge_contract变为recharge_contract_id用于API调用
      if (data.recharge_contract) {
        data.recharge_contract_id = data.recharge_contract;
        delete data.recharge_contract;
      }
      
      // 备注字段已经直接使用remark，无需转换
      
      if (projectDrawerType.value == 0) {
        ElLoading.service({ fullscreen: true, text: '创建中...' });
        
        addTasks(data).then(res => {
          ElLoading.service().close();
          if (res && res.code === 990) {
            ElMessage.success("创建成功！");
            projectAddDrawer.value = false;
            getTaskList();
          } else {
            ElMessage.error(res?.msg || "创建失败");
          }
        }).catch(err => {
          ElLoading.service().close();
          console.error("创建任务失败:", err);
          ElMessage.error("创建失败");
        });
      } else {
        ElLoading.service({ fullscreen: true, text: '更新中...' });
        
        updateTasks(data).then(res => {
          ElLoading.service().close();
          if (res && res.code === 990) {
            ElMessage.success("修改成功！");
            projectAddDrawer.value = false;
            getTaskList();
          } else {
            ElMessage.error(res?.msg || "修改失败");
          }
        }).catch(err => {
          ElLoading.service().close();
          console.error("修改任务失败:", err);
          ElMessage.error("修改失败");
        });
      }
    } else {
      console.log("form validation failed:", fields);
      ElMessage.warning("请填写完整表单信息");
    }
  });
};

const onSubmitAndNext = async formEl => {
  console.log(form.value, "formEl");
  if (!formEl) return;
  
  const loading = ElLoading.service({
    lock: true,
    text: '提交中...',
    background: 'rgba(0, 0, 0, 0.7)'
  });
  
  try {
    const valid = await formEl.validate().catch(err => {
      console.log("validation error", err);
      return false;
    });
    
    if (!valid) {
      loading.close();
      return;
    }
    
    let data = JSON.parse(JSON.stringify(form.value));
    
    console.log("Form submission data (onSubmitAndNext):", {
      platform_type: data.platform_type,
      platform_type_entity: data.platform_type_entity,
      complete_data: data
    });
    
    // 如果task_type是空字符串，将其设置为0
    if (data.task_type === "") {
      console.log("Converting empty task_type to 0");
      data.task_type = 0;
    }
    
    let project_info = options.value.find(item => item.id === data.project_id);
    
    if (!project_info) {
      ElMessage.error("项目信息获取失败");
      loading.close();
      return;
    }
    
    data.project_name = project_info.project_name;
    
    if (!data.contract_id) {
      ElMessage.warning('请选择客户合同');
      loading.close();
      return;
    }
    
    if (!data.customer_entity) {
      ElMessage.warning('客户主体不能为空，请选择所属项目');
      loading.close();
      return;
    }
    
    data.customer_contract_id = data.contract_id;
    
    // Map the recharge_contract field to recharge_contract_id expected by the API
    if (data.recharge_contract) {
      data.recharge_contract_id = data.recharge_contract;
    }
    
    if (isCurrentObj.value) {
      data.contract_sign_date = isCurrentObj.value.contract_sign_date || data.contract_sign_date || "";
      data.contract_start_date = isCurrentObj.value.contract_start_date || data.contract_start_date || "";
      data.contract_end_date = isCurrentObj.value.contract_end_date || data.contract_end_date || "";
    }
    
    console.log("Submit data with contract dates:", {
      contract_sign_date: data.contract_sign_date,
      contract_start_date: data.contract_start_date,
      contract_end_date: data.contract_end_date,
      recharge_contract_id: data.recharge_contract_id // Log the mapped field
    });
    
    if (!data.performance_owner || !data.performance_department) {
      ElMessage.warning('业绩归属人和业绩归属部门不能为空，请选择包含这些信息的客户合同');
      loading.close();
      return;
    }
    
    if (!data.order_method) {
      ElMessage.warning('请选择下单方式');
      loading.close();
      return;
    }
    
    // 验证集采线下下单时必须选择账户
    if (data.order_method === 2 && !data.account_id) {
      ElMessage.warning('集采线下下单时必须选择账户');
      loading.close();
      return;
    }
    
    // 确保集采线下下单时包含账户品牌名称
    if (data.order_method === 2 && data.account_id && !data.account_brand_name) {
      console.log('账户品牌名称为空，使用默认值');
    }
    
    data.operator = data.performance_owner;
    data.department = data.performance_department;
    
    data.platform_type = form.value.platform_type;
    data.platform_type_entity = form.value.platform_type_entity;
    
    data.platform_type = Number(data.platform_type);
    
    // 重命名充值合同字段，从recharge_contract变为recharge_contract_id用于API调用
    if (data.recharge_contract) {
      data.recharge_contract_id = data.recharge_contract;
      delete data.recharge_contract;
    }
    
    // 备注字段已经直接使用remark，无需转换
    
    if (projectDrawerType.value == 0) {
      // Creating a new task
      const res = await addTasks(data);
      
      if (res && res.code === 990) {
        ElMessage.success("创建成功！");
        projectAddDrawer.value = false;
        
        if (res.data && res.data.data && res.data.data.id) {
          router.push({ 
            path: "/business/task/informationAuthor", 
            query: { 
              page_type: "add", 
              id: res.data.data.id 
            } 
          });
        } else {
          ElMessage.warning("未能获取任务ID，无法跳转到下一步");
          getTaskList();
        }
      } else {
        ElMessage.error(res?.msg || "创建失败");
      }
    } else {
      // Editing existing task
      const res = await updateTasks(data);
      
      if (res && res.code === 990) {
        ElMessage.success("修改成功！");
        projectAddDrawer.value = false;
        
        if (data.task_id) {
          router.push({ 
            path: "/business/task/informationAuthor", 
            query: { 
              page_type: "add", 
              id: data.task_id 
            } 
          });
        } else {
          ElMessage.warning("未能获取任务ID，无法跳转到下一步");
          getTaskList();
        }
      } else {
        ElMessage.error(res?.msg || "修改失败");
      }
    }
  } catch (error) {
    console.error("提交表单时发生错误:", error);
    ElMessage.error("提交失败，请稍后重试");
  } finally {
    loading.close();
  }
};

const createdTask = (i, e) => {
  projectAddDrawer.value = true;
  getMediaPlatformOptions();
  if (i == 1) {
    projectDrawerType.value = 0;
    
    // 重置初始加载标志，新建任务不需要保留充值合同
    isInitialFormLoad.value = false;
    
    form.value = {
      project_id: "",
      task_name: "",
      promotion_platforms_genres: 1,
      task_type: "",
      settlement_method: 1,
      order_type: 1,
      oa_project_id: "",
      customer_entity: "",
      customer_contract: "",
      contract_id: "",
      usc_code: "", // 重置客户主体的USC代码字段
      performance_owner: "",
      performance_department: "",
      platform_type: 0,
      platform_type_entity: "", // This should match the entity for platform_type 1
      order_method: "",
      recharge_contract: "",
      remark: "",
      // 重置账户相关字段
      account_id: "",
      account_name: "",
      account_brand_name: "", // 重置账户品牌名称
      // Additional fields required by API
      customer_code: "",
      approval_type_text: "",
      contract_type: "",
      media_or_channel: "",
      contract_sign_date: "",
      contract_start_date: "",
      contract_end_date: "",
      contract_amount: "",
      contract_relative_name: "",
      company: "",
      oa_project_name: "",
      operator: "",
      department: ""
    };

    // Set platform_type_entity based on platform_type
    const platformTypeNum = Number(form.value.platform_type);
    switch(platformTypeNum) {
      case 1:
        form.value.platform_type_entity = "武汉星图新视界科技有限公司";
        break;
      case 2:
        form.value.platform_type_entity = "武汉星图新视界科技有限公司";
        break;
      case 3:
        form.value.platform_type_entity = "薯鸿文化传媒（上海）有限公司";
        break;
      case 4:
        form.value.platform_type_entity = "北京晨钟科技有限公司";
        break;
      case 5:
        form.value.platform_type_entity = "上海东方传媒（集团）有限公司‌‌";
        break;
      case 6:
        form.value.platform_type_entity = "深圳市腾讯文化传媒有限公司";
        break;
      default:
        form.value.platform_type_entity = "";
    }
    
    remoteMethodSelect();
    return;
  }
  
  console.log("Creating/editing task with data:", {
    task_id: e?.id,
    has_participants: !!e?.participants,
    participants_customer_code: e?.participants?.customer_code,
    customer_code: e?.customer_code
  });
  
  edit(e);
};

const closeDrawer = () => {
  projectAddDrawer.value = false;
  projectDrawerType.value = 0;
  form.value = {
    project_id: "",
    task_name: "",
    promotion_platforms_genres: 1,
    task_type: "",
    settlement_method: 1, // Default to 1 "自运营"
    oa_project_id: "",
    customer_entity: "",
    customer_contract: "",
    contract_id: "", // Make sure to reset contract_id
    usc_code: "", // 重置客户主体的USC代码字段
    performance_owner: "",
    performance_department: "",
    platform_type: 0,
    platform_type_entity: "", // This should match the entity for platform_type 1
    order_method: "",
    recharge_contract: "",
    remark: "",
    // Add task_type field reset
    task_type: "",
    // 重置账户相关字段
    account_id: "",
    account_name: "",
    account_brand_name: "", // 重置账户品牌名称
    // Additional fields required by API
    customer_code: "",
    approval_type_text: "",
    contract_type: "",
    media_or_channel: "",
    contract_sign_date: "",
    contract_start_date: "",
    contract_end_date: "",
    contract_amount: "",
    contract_relative_name: "",
    company: "",
    oa_project_name: "",
    operator: "",
    department: ""
  };
  // Reset the project options to prevent stale data
  options.value = [];
  // Reset contract list
  customerUscList.value = [];
  // Reset contract details
  isCurrentObj.value = null;
  // Reset OA project source indicator
  oaProjectSource.value = '未知';
  // 重置账户选项
  accountOptions.value = [];
  selectedAccount.value = null;
  savedSelectedAccount.value = null; // 重置保存的选中账户信息
  selectedAccountId.value = ''; // 重置单选按钮的选中状态
  accountPagination.value = {
    currentPage: 1,
    pageSize: 10,
    total: 0
  };
  accountSearchForm.value = {
    customer_account_id: '',
    account_name: '',
    customer_name: '',
    media_platform: ''
  };
};
// 表格配置项
const columns = reactive<ColumnProps<Business.ReqBusinessProjectParams>[]>([
  {
    prop: "platform_type",
    label: "平台",
    search: {
      el: "select",
      props: {
        placeholder: "全部"
      }
    },
    enum: [
      {
        label: "抖音",
        value: 1
      },
      {
        label: "小红书",
        value: 3
      },
      {
        label: "快手",
        value: 4
      },
      {
        label: "B站",
        value: 5
      },
      {
        label: "腾讯互选",
        value: 6
      }
    ]
  },
  {
    prop: "order_method",
    label: "下单方式",
    search: {
      el: "select",
      props: {
        placeholder: "请选择下单方式",
      }
    },
    enum: [
      {
        label: "星推下单",
        value: 1
      },
      {
        label: "线下下单",
        value: 2
      }
    ]
  },
  {
    prop: "task_search",
    label: "任务信息",
    search: {
      el: "input",
      props: { placeholder: "请输入任务名称或ID" }
    }
  },
  {
    prop: "custom_name",
    label: "客户名称",
    search: {
      el: "input",
      props: { placeholder: "请输入客户名称" }
    }
  },
  {
    prop: "project_name",
    label: "项目名称",
    search: {
      el: "input",
      props: { placeholder: "请输入项目名称" }
    }
  },
  {
    prop: "task_time",
    label: "创建时间",
    search: {
      el: "date-picker",
      span: 1,
      props: { 
        type: "daterange", 
        valueFormat: "YYYY-MM-DD",
        rangeSeparator: "至",
        startPlaceholder: "开始时间",
        endPlaceholder: "结束时间"
      },
      defaultValue: task_time.value
    }
  },
  { prop: "operation", label: "操作", fixed: "right", width: 100 }
]);

// 过滤需要搜索的配置项 && 排序
const searchColumns = computed(() => {
  return columns?.filter(item => item.search?.el || item.search?.render).sort((a, b) => a.search!.order! - b.search!.order!);
});

// 设置 搜索表单默认排序 && 搜索表单项的默认值
searchColumns.value?.forEach((column, index) => {
  column.search!.order = column.search?.order ?? index + 2;
  const key = column.search?.key ?? handleProp(column.prop!);
  const defaultValue = column.search?.defaultValue;
  if (defaultValue !== undefined && defaultValue !== null) {
    searchParam.value[key] = defaultValue;
    // searchInitParam.value[key] = defaultValue;
  }
});
//跳转去选号
const goToOrderPath = (path, row) => {
  if (path == "createOrder") {
    xingtuTaskOrderKolInfo(row.id);
  } else {
    router.push({ path: "/business/task/" + path, query: { page_type: "add", id: row.id } });
  }
};

//跳转修改
const handleEdit = row => {
  router.push({ path: "/business/task/informationAuthor", query: { page_type: "edit", id: row.id } });
};

//跳转变更
const goToTaskChange = row => {
  router.push({ path: "/business/task/informationAuthor", query: { page_type: "change", id: row.id } });
};

//跳转任务详情
const goToTaskOrderDetail = row => {
  router.push({ path: "/business/task/informationAuthor", query: { page_type: "info", id: row.id } });
};
const expediting = row => {
  socketStore.sendMessage(
    "urgent_message",
    JSON.stringify({
      task_id: row.id
    })
  );
};
const dialogOrderVisible = ref(false);
const gridOrderData = ref([]);
const task_id = ref("");
const selectedOrders = ref([]); // 用于保存选中状态的数组
const selectAll = ref(false); // 控制全选复选框
const placementListArr = [
  {
    label: "1-20S视频",
    value: 1
  },
  {
    label: "21-60S视频",
    value: 2
  },
  {
    label: "60S以上视频",
    value: 71
  },
  {
    label: "招募任务一口价",
    value: 100
  }
];

//删除
const deleteTask = row => {
  ElMessageBox.confirm("确认删除该任务吗？")
    .then(() => {
      deleteTasks({ task_id: row.id }).then(res => {
        if (res.code == 990) {
          ElMessage.success("删除成功！");
          checkboxGroup1.value = [""];
          getTaskList();
        }
      });
    })
    .catch(() => {});
};

//撤回
const cancelTask = row => {
  ElMessageBox.confirm("确认撤回该任务吗？")
    .then(() => {
      cancelTaskApi({ task_id: row.id }).then(res => {
        if (res.code == 990) {
          ElMessage.success("撤回成功！");
          checkboxGroup1.value = ["2"];
          getTaskList();
        }
      });
    })
    .catch(() => {});
};

// 初始化时，重置 selectedOrders 数组并监控数据变化
watch(
  gridOrderData,
  newData => {
    // 初始化复选框状态，只有 `order_status === 0` 时可选中
    selectedOrders.value = newData.map(row => (row.order_status === 0 ? false : null));
  },
  { immediate: true }
);

// 监听 selectedOrders，检测是否所有符合条件的行都被选中
watch(selectedOrders, newSelected => {
  const allSelected = gridOrderData.value.every((row, index) => {
    return row.order_status !== 0 || newSelected[index];
  });
  selectAll.value = allSelected;
});

// 复选框改变时的回调
const changeOrderInfo = index => {
  // 更新当前选中的状态
  selectedOrders.value[index] = !selectedOrders.value[index];
  // 检查是否需要取消全选状态
  const allSelected = gridOrderData.value.every((row, idx) => {
    return row.order_status !== 0 || selectedOrders.value[idx];
  });
  selectAll.value = allSelected;
};

// 全选/取消全选
const toggleSelectAll = () => {
  gridOrderData.value.forEach((row, index) => {
    if (row.order_status === 0) {
      selectedOrders.value[index] = selectAll.value;
    }
  });
};

const confirmOrder = () => {
  // 返回选中的表格数据
  const selectedData = gridOrderData.value.filter((_, index) => selectedOrders.value[index]);
  const data = selectedData.map(item => {
    return item.id.toString();
  });
  if (data.length > 0) {
    localStorage.setItem("kol_info", JSON.stringify(data));
    router.push({ path: "/business/task/createOrder", query: { page_type: "add", id: task_id.value } });
  } else {
    ElMessage.warning("请选择达人");
  }
};


// 下单前流程
const getTaskList = async () => {
  try {
    loading.value = true;
    if (!props.isSearch) {
      formInline.value.project_id = props.projectId;
    }
    formInline.value.status = checkboxGroup1.value[0];
    
    // Handle task_search parameter - ensure it's a string
    if (sessionStorage.getItem("task_id")) {
      formInline.value.task_search = sessionStorage.getItem("task_id");
    }
    
    const res = await searchTasks(formInline.value);
    
    sessionStorage.removeItem("task_id");
    sessionStorage.removeItem("status"); 
    sessionStorage.removeItem("active");

    // 当 res.data 为 null 时重置数据
    if (!res?.data) {
      tableData.value = [];
      query.total = 0;
      checkboxOptions.value = [
        { label: `全部（0）`, value: "", count: 0 },
        { label: `待填写下单信息（0）`, value: "2", count: 0 },
        { label: `待审核（0）`, value: "3", count: 0 },
        { label: `审核拒绝（0）`, value: "4", count: 0 },
        { label: `待下单（0）`, value: "6", count: 0 },
        { label: `已完成（0）`, value: "7", count: 0 }
      ];
      return;
    }

    // 正常数据处理逻辑
    const searchDate = res.data.tasks;
    const total = res.data.count;
    tableData.value = searchDate;
    query.total = total;

    let checkboxArr = res?.status_aggregate;
    //求checkboxArr的value的和
    let sum = 0;
    Object.values(checkboxArr || {}).forEach(item => {
      sum += item;
    });

    checkboxOptions.value = [
      { label: `全部（${sum || 0}）`, value: "", count: sum || 0 },
      { label: `待填写下单信息（${checkboxArr[2] || 0}）`, value: "2", count: checkboxArr[2] || 0 },
      { label: `待审核（${checkboxArr[3] || 0}）`, value: "3", count: checkboxArr[3] || 0 },
      { label: `审核拒绝（${checkboxArr[4] || 0}）`, value: "4", count: checkboxArr[4] || 0 },
      { label: `待下单（${checkboxArr[6] || 0}）`, value: "6", count: checkboxArr[6] || 0 },
      { label: `已完成（${checkboxArr[7] || 0}）`, value: "7", count: checkboxArr[7] || 0 }
    ];
  } catch (error) {
    console.error("获取任务列表失败:", error);
  } finally {
    loading.value = false;
  }
};

//获取下单达人信息
const xingtuTaskOrderKolInfo = id => {
  xingtuTaskOrderKolInfoApi({ task_id: id }).then(res => {
    gridOrderData.value = res.data;
    task_id.value = id;
    dialogOrderVisible.value = true;
  });
};

const query = reactive({
  total: 0
});


const formInline = ref({
  task_type: "",
  task_search: "",
  custom_name: "",
  project_name: "",
  project_id: "",
  status: "",
  order_status: "",
  created_id: "",
  created_name: "",
  task_create_time: "",
  task_end_time: "",
  page_size: 10,
  page: 1
});

const zoom = ref("");

const detectZoom = () => {
  let ratio = 0;
  const screen = window.screen;
  const ua = navigator.userAgent.toLowerCase();
  if (window.devicePixelRatio !== undefined) {
    ratio = window.devicePixelRatio;
  } else if (~ua.indexOf("msie")) {
    if (screen.deviceXDPI && screen.logicalXDPI) {
      ratio = screen.deviceXDPI / screen.logicalXDPI;
    }
  } else if (window.outerWidth !== undefined && window.innerWidth !== undefined) {
    ratio = window.outerWidth / window.innerWidth;
  }
  if (ratio) {
    ratio = Math.round(ratio * 100);
  }
  return ratio;
};
const contract_id = ref("");
const taskDetail = ref("");

const contractLoading = ref(false);

//通过usc_code查询合同
const contractInformationListFun = (usc_code, contract_id) => {
  if (!usc_code) return;
  
  contractLoading.value = true;
  
  contractInformationList({
    usc_code: usc_code,
    contract_id: contract_id || "", // 使用 contract_id 进行搜索
    page: 1,
    page_size: 999
  }).then(res => {
    if (res && res.code === 990 && res.data && Array.isArray(res.data.lists)) {
      console.log("Contract list response:", res);
      
      const hasProjectNames = res.data.lists.some(contract => contract.project_name);
      console.log("Contracts have project_name field:", hasProjectNames);
      
      customerUscList.value = res.data.lists;
      
      if (customerUscList.value.length === 1 && !contract_id) {
        form.value.customer_contract = customerUscList.value[0].contract_id;
        contract_id_change(customerUscList.value[0].contract_id);
      }
      
      if (customerUscList.value.length === 0) {
        ElMessage.warning('未找到该客户的合同信息');
      }
    } else {
      customerUscList.value = [];
      ElMessage.warning('获取合同信息失败');
    }
  }).catch(err => {
    console.error("获取合同信息失败:", err);
    ElMessage.error('获取合同信息失败');
    customerUscList.value = [];
  }).finally(() => {
    contractLoading.value = false;
  });
};

//筛选合同
const searchContract = (query) => {
  // 将搜索内容保存到 contract_id
  contract_id.value = query;

  // 优先使用表单中的usc_code（客户主体修改后会更新），如果没有则使用项目的usc_code
  let uscCode = form.value.usc_code;

  // 如果表单中没有usc_code，则从项目中获取
  if (!uscCode) {
    const selectedProject = options.value.find(project => project.id === form.value.project_id);
    uscCode = selectedProject?.usc_code;
  }

  // 调用合同查询接口,传入usc_code和搜索内容
  if (uscCode) {
    contractLoading.value = true;
    contractInformationListFun(uscCode, query);
  } else {
    ElMessage.warning('请先选择项目或客户主体');
  }
};

const isCurrentObj = ref();

// Add a utility function to extract project name from contract data
const extractProjectName = (contract) => {
  if (!contract) return "";
  
  // Check all possible field names for project name in order of priority
  return contract.project_name || 
         contract.contract_project_name || 
         contract.oa_project_name || 
         contract.project_id || 
         contract.oa_project_id ||
         contract.contract_name || 
         "";
};

const contract_id_change = val => {
  if (!val) {
    // 如果清空客户合同，同时清空充值合同
    form.value.recharge_contract = "";
    rechargeContractOptions.value = [];
    return;
  }
  
  const selectedContract = customerUscList.value.find(item => item.contract_id === val);
  if (!selectedContract) return;
  
  // Store the selected contract in the ref
  isCurrentObj.value = selectedContract;
  
  // Log the full contract object to see all available fields
  console.log("Selected contract data:", selectedContract);
  
  // Extract project name using the utility function
  const projectName = extractProjectName(selectedContract);
  
  if (projectName) {
    console.log("Found project name:", projectName);
    // Apply to OA project fields
    form.value.oa_project_id = projectName;
    form.value.oa_project_name = projectName;
    
    // Set source indicator based on which field was used
    if (selectedContract.project_name) {
      oaProjectSource.value = '合同项目名称';
    } else if (selectedContract.contract_project_name) {
      oaProjectSource.value = '合同项目名称';
    } else if (selectedContract.oa_project_name) {
      oaProjectSource.value = '合同OA项目名称';
    } else if (selectedContract.project_id) {
      oaProjectSource.value = '项目ID';
    } else if (selectedContract.oa_project_id) {
      oaProjectSource.value = '合同OA项目ID';
    } else {
      oaProjectSource.value = '合同名称';
    }
  } else {
    // No valid project name found - set default
    form.value.oa_project_id = selectedContract.contract_id || "";
    oaProjectSource.value = '合同编号';
  }
  
  // Store the contract ID in customer_code and the contract NAME in customer_contract
  form.value.customer_code = selectedContract.contract_id || "";
  form.value.customer_contract = selectedContract.contract_name || "";
  
  // Track the contract ID separately for API calls
  form.value.contract_id = selectedContract.contract_id || "";
  
  // Additional contract metadata
  form.value.approval_type_text = selectedContract.approval_type_text || "";
  form.value.contract_type = selectedContract.contract_type || "";
  form.value.media_or_channel = selectedContract.media_or_channel || form.value.platform_type_entity;
  
  // Date fields
  form.value.contract_sign_date = selectedContract.contract_sign_date || "";
  form.value.contract_start_date = selectedContract.contract_start_date || "";
  form.value.contract_end_date = selectedContract.contract_end_date || "";
  
  // Financial info
  form.value.contract_amount = selectedContract.contract_amount || "";
  
  // Company and partner info
  form.value.contract_relative_name = selectedContract.relative_party_name || selectedContract.contract_relative_name || "";
  form.value.company = selectedContract.company || "";
  
  // Set performance owner and department from contract data
  form.value.performance_owner = selectedContract.perf_owner_detail || "";
  form.value.performance_department = selectedContract.perf_dept_detail || "";
  
  // Check if performance data is missing and warn the user
  if (!selectedContract.perf_owner_detail || !selectedContract.perf_dept_detail) {
    ElMessage.warning('所选合同缺少业绩归属人或业绩归属部门信息，请选择包含完整信息的合同');
  }
  
  // Copy performance owner/department to operator/department fields
  form.value.operator = form.value.performance_owner;
  form.value.department = form.value.performance_department;
  
  // NOTE: We don't need to load recharge contracts here as the contract_id watcher will handle it
  // This avoids potential duplicate loading of recharge contracts
  console.log("Contract changed to:", val, "recharge contract will be loaded by watcher if needed");
};

// Add watcher for performance_owner to sync with operator
watch(() => form.value.performance_owner, (newValue) => {
  form.value.operator = newValue;
});

// Add watcher for performance_department to sync with department
watch(() => form.value.performance_department, (newValue) => {
  form.value.department = newValue;
});

// Watch platform_type to determine if recharge_contract is needed and set platform_type_entity
watch(() => form.value.platform_type, (newValue) => {
  const platformTypeNum = Number(newValue || 0);

  // Update platformLabel to display the current platform name
  const platformOption = mediaPlatformOptions.value.find(item => item.value === platformTypeNum);
  platformLabel.value = platformOption ? platformOption.label : '';

  // Update platform_type_entity based on platform type
  switch(platformTypeNum) {
    case 1:
      form.value.platform_type_entity = "武汉星图新视界科技有限公司";
      break;
    case 2:
      form.value.platform_type_entity = "武汉星图新视界科技有限公司";
      break;
    case 3:
      form.value.platform_type_entity = "薯鸿文化传媒（上海）有限公司";
      break;
    case 4:
      form.value.platform_type_entity = "北京晨钟科技有限公司";
      break;
    case 5:
      form.value.platform_type_entity = "上海东方传媒（集团）有限公司‌‌";
      break;
    case 6:
      form.value.platform_type_entity = "深圳市腾讯文化传媒有限公司";
      break;
    default:
      form.value.platform_type_entity = "";
  }

  // 修正条件判断逻辑：先检查是否需要充值合同
  if (isRechargeContractRequired()) {
    // 如果需要充值合同，并且已选择了客户合同，则加载充值合同
    if (form.value.contract_id) {
      console.log("Platform type changed, loading recharge contracts for current contract");
      loadRechargeContracts(form.value.contract_id, form.value.recharge_contract, false);
    }
  } else {
    // 如果不需要充值合同，清空充值合同值
    form.value.recharge_contract = "";
    rechargeContractOptions.value = [];
  }
});

// Improve the contract_id watcher
watch(() => form.value.contract_id, (newValue, oldValue) => {
  console.log("Contract ID changed:", oldValue, "->", newValue);
  
  // 如果是初始加载阶段，不清空充值合同值
  if (isInitialFormLoad.value) {
    console.log("Skipping recharge contract clear during initial form load");
    return;
  }
  
  if (newValue !== oldValue) {
    // Always clear recharge contract when contract_id changes
    form.value.recharge_contract = "";
    
    // Clear recharge contract options to ensure a fresh load on focus
    rechargeContractOptions.value = [];
    
    // Reset debounce timer and lastRechargeContractRequest timestamp
    if (rechargeContractDebounceTimer) {
      clearTimeout(rechargeContractDebounceTimer);
      rechargeContractDebounceTimer = null;
    }
    lastRechargeContractRequest = 0;
    
    console.log("Cleared recharge contract and options due to contract change");
    
    // 新增: 如果当前任务需要充值合同，自动加载充值合同选项
    if (isRechargeContractRequired()) {
      console.log("Auto-loading recharge contracts for new contract selection");
      loadRechargeContracts(newValue, "", false);
    }
  }
});

// Helper function to clear task filter on refresh
const setupTaskFilterReset = () => {
  // Listen for beforeunload to detect page refresh
  window.addEventListener('beforeunload', () => {
    // Only clear filter if it was set by task_id parameter
    if (route.query.task_id) {
      sessionStorage.removeItem("task_id");
    }
  });
};
const closeCurrentTab = () => {
  tabStore.removeTabs(route.fullPath);
};

onMounted(() => {
  // Setup the refresh handler
  setupTaskFilterReset();
  
  checkboxGroup1.value = [""];
  checkboxGroup2.value = [""];

  if (sessionStorage.getItem("active")) {
    activeName.value = sessionStorage.getItem("active") == "1" ? "first" : "second";
    if (sessionStorage.getItem("active") == "2") {
      checkboxGroup2.value = [sessionStorage.getItem("status")];
    } else {
      checkboxGroup1.value = [sessionStorage.getItem("status")];
    }
  }

  // 运行任务调试功能来帮助排查问题
  if (route.query.task_id) {
    console.log("[DEBUG] Running task debugger...");
    debugTask();
  }

  // 处理 task_id 参数（在 URL 中的参数）
  if (route.query.task_id && isFirstLoad.value) {
    // 处理 task_id 可能是数组的情况
    const taskId = Array.isArray(route.query.task_id) 
      ? route.query.task_id[0] 
      : route.query.task_id;
    

    // 当是首次加载时，按 task_id 筛选 - 使用本地变量避免类型问题
    const taskIdString = String(taskId);
    
    // 尝试同时设置多个可能的搜索参数，增加找到目标任务的概率
    formInline.value.task_search = taskIdString;
    formInline.value.id = taskIdString;         // 尝试另一个可能的参数名
    formInline.value.task_id = taskIdString;    // 再尝试一个可能的参数名
    
    // 显式检查是否为数字ID，如果是，可能需要强制使用精确匹配
    if (/^\d+$/.test(taskIdString)) {
      // 临时存储在 sessionStorage 中，以确保 getTaskList 能找到它
      sessionStorage.setItem("task_id", taskIdString);
    }
    
    // 首次加载完成后重置标志
    isFirstLoad.value = false;
    
    // 直接调用搜索
    console.log("[DEBUG] Calling getTaskList with task_id:", taskIdString);
    getTaskList();
    
    // 从 URL 中移除 task_id 参数，但不刷新页面
    const newQuery = { ...route.query };
    delete newQuery.task_id;
    closeCurrentTab();
    router.replace({ 
      path: route.path, 
      query: newQuery 
    });
    
    return;
  } else if (route.query.id) {
    // 处理原有的 id 参数
    if (route.query.key == "4-2") {
      getTaskList();
      return;
    }
    
    // 处理 id 可能是数组的情况
    const id = Array.isArray(route.query.id) ? route.query.id[0] : route.query.id;
    formInline.value.task_search = id;
    sessionStorage.setItem("task_id", id);
    getTaskList();
    return;
  } else if (route.query.project_id) {
    // 处理 project_id 可能是数组的情况
    const projectId = Array.isArray(route.query.project_id) 
      ? route.query.project_id[0] 
      : route.query.project_id;
    
    formInline.value.project_id = projectId;
    getTaskList();
    return;
  }
  
  // 默认加载全部任务
  _search();

  zoom.value = detectZoom();
  window.addEventListener("resize", () => {
    zoom.value = detectZoom();
  });

  pageFun();
  getUpTaskList();
  getOptionsApi();
  // 如果任务ID存在，则初始化数据
  if (taskId.value) {
    formStatus.value = false;
    initGetTask();
    setTimeout(() => {
      // Initialize platformLabel based on current platform_type if it exists
      if (form.value.platform_type) {
        const platformOption = mediaPlatformOptions.value.find(item => item.value === form.value.platform_type);
        platformLabel.value = platformOption ? platformOption.label : '';
      }
    }, 500);
  }
});

onBeforeUnmount(() => {
  // 移除刷新事件监听器
  window.removeEventListener('beforeunload', () => {});
  
  window.removeEventListener("resize", () => {
    zoom.value = detectZoom();
  });
});

// 订单类型选项
const orderTypeOptions = ref([
  { label: "自运营", value: 1 },
  { label: "走单（不含代理服务费）", value: 2 },
  { label: "代下单（含代理服务费）", value: 3 },
  { label: "资源包订单", value: 4 },
  // { label: "水下订单", value: 5 },
]);

const mediaPlatformOptions = ref([
  { label: "抖音", value: 1 },
  { label: "小红书", value: 3 },
  { label: "快手", value: 4 },
  { label: "B站", value: 5 },
  { label: "腾讯互选", value: 6 }
]);

const mediaPlatforms = ref();

// 获取媒体平台列表的函数
const getMediaPlatformOptions = async () => {
  try {
    const response = await oaMediaListApi({});
    if (response.code == 990 && response.data) {
      console.log(response.data);
      mediaPlatforms.value = response.data.list
      // 更新媒体平台选项
    } else {
      console.warn('获取媒体平台列表失败，使用默认选项');
    }
  } catch (error) {
    console.error('获取媒体平台列表出错:', error);
    // 保持使用默认的静态选项
  }
};

const orderMethodOptions = ref([
  { label: "星推下单", value: "1" },
  { label: "集采线下下单", value: "2" }
]);

const rechargeContractOptions = ref([
  { label: "请选择充值合同", value: "" }
]);

// 添加一个防抖计时器变量和上次请求时间记录
let rechargeContractDebounceTimer = null;
let lastRechargeContractRequest = 0;

// Add search query and filtered contracts state
const rechargeContractSearchQuery = ref('');
const filteredRechargeContracts = computed(() => {
  if (!rechargeContractSearchQuery.value) {
    return rechargeContractOptions.value;
  }
  
  // Filter the options locally based on search query
  return rechargeContractOptions.value.filter(option => 
    option.label.toLowerCase().includes(rechargeContractSearchQuery.value.toLowerCase())
  );
});

// Local filter function for recharge contracts
const localFilterRechargeContracts = (query) => {
  rechargeContractSearchQuery.value = query;
  // If query length is sufficient and we have a contract ID, also trigger remote search
  if (query.length >= 2 && form.value.contract_id) {
    // Use a small timeout to avoid too many API calls
    if (rechargeContractDebounceTimer) {
      clearTimeout(rechargeContractDebounceTimer);
    }
    
    rechargeContractDebounceTimer = setTimeout(() => {
      searchRechargeContract(query);
    }, 500); // 500ms debounce
  }
  
  // Return true to let el-select's built-in filtering use our computed property
  return true;
};

// Clear function for recharge contract field
const clearRechargeContract = () => {
  form.value.recharge_contract = "";
  rechargeContractSearchQuery.value = "";
};

// Add remote method handler for recharge contract search
const searchRechargeContract = (query) => {
  // Update the local search query for local filtering
  rechargeContractSearchQuery.value = query;
  
  // For very short queries, just use local filtering without API call
  if (query && query.length <= 1) {
    console.log(`Query "${query}" is too short, using only local filtering`);
    return;
  }
  // Check if this platform requires recharge contract
  if (!isRechargeContractRequired()) {
    ElMessage.info('当前媒体平台不需要选择充值合同');
    return;
  }

  // Check if client contract is selected
  if (!form.value.contract_id) {
    ElMessage.warning('请先选择客户合同，然后才能搜索充值合同');
    return;
  }

  console.log(`Searching recharge contracts with query "${query}"`);

  // Call the load function with preventRecursion=true to avoid unnecessary recursive calls
  loadRechargeContracts(form.value.contract_id, query, true);
};

// Add loading state for recharge contract select
const rechargeContractLoading = ref(false);

// Modify the loadRechargeContracts function to better support editing
const loadRechargeContracts = (customer_code = "", selectedValue = "", preventRecursion = false) => {
  // Exit early if no customer code is provided
  if (!customer_code) {
    console.warn("No customer_code provided to loadRechargeContracts, aborting");
    rechargeContractOptions.value = [];
    rechargeContractSearchQuery.value = ''; // Reset search query
    rechargeContractLoading.value = false;
    return;
  }

  // 如果是在初始加载阶段，保存当前值以便稍后恢复
  const currentValue = isInitialFormLoad.value ? form.value.recharge_contract : null;
  
  console.log(`Loading recharge contracts for customer ${customer_code}${selectedValue ? ` with selected value: ${selectedValue}` : ''}${currentValue ? `, current value: ${currentValue}` : ''}`);

  // 检查是否是短时间内的重复调用 - 但如果是Focus事件触发的调用，我们希望它总是执行
  const now = Date.now();
  const minDelay = 300; // 至少300ms的间隔
  
  // 如果上次请求是很久之前（超过5秒），我们肯定要加载新数据
  const forceRefresh = (now - lastRechargeContractRequest > 5000);
  
  if (!forceRefresh && now - lastRechargeContractRequest < minDelay) {
    // 如果是短时间内重复调用，使用防抖
    if (rechargeContractDebounceTimer) {
      clearTimeout(rechargeContractDebounceTimer);
    }
    
    console.log("Debouncing recharge contract request");
    rechargeContractDebounceTimer = setTimeout(() => {
      lastRechargeContractRequest = Date.now();
      loadRechargeContracts(customer_code, selectedValue, preventRecursion);
    }, minDelay);
    
    return;
  }
  
  // 记录本次请求时间
  lastRechargeContractRequest = now;
  
  rechargeContractLoading.value = true;
  
  // 构建查询参数，包含customer_code和可选的搜索关键词
  const params = { 
    customer_code,
    search: selectedValue || "",
    page: 1, 
    page_size: 999 
  };
  
  console.log("Sending recharge contract request with params:", params);
  
  getRechargeContract(params).then(res => {
    console.log("Recharge contract API response:", res);
    
    // 如果是在初始加载阶段，并且有当前值，确保不会丢失
    if (isInitialFormLoad.value && currentValue) {
      console.log("Preserving recharge contract value during initial load:", currentValue);
    }
    
    if (res && res.code === 990 && res.data) {
      const contractList = Array.isArray(res.data.list) ? res.data.list : [];
      
      console.log("Loaded recharge contracts:", contractList.length);
      
      // 如果没有找到充值合同，提示用户
      if (contractList.length === 0 && !selectedValue) {
        ElMessage.warning(`未找到客户合同 ${customer_code} 对应的充值合同`);
        rechargeContractOptions.value = [];
        rechargeContractSearchQuery.value = ''; // Reset search query
        
        // 即使没有找到充值合同，在初始加载阶段也要恢复之前的值
        if (isInitialFormLoad.value && currentValue) {
          console.log("Restoring recharge contract value when no contracts found:", currentValue);
          form.value.recharge_contract = currentValue;
        }
      } else {
        // Convert API response to options format
        const contractOptions = contractList.map(contract => {
          // 获取合同金额，支持多种可能的字段名
          const contractAmount = contract.customer_contract_amount;

          const label = `${contract.recharge_contract_id} - ${contract.customer_contract_name || '无合同名称'}${contractAmount ? ' (¥' + contractAmount + ')' : ''}`;
          return {
            label: label,
            value: contract.recharge_contract_id,
            id: contract.id,
            customer_contract_name: contract.customer_contract_name,
            customer_contract_number: contract.customer_contract_number,
            contract_amount: contractAmount || '' // 保存合同金额字段
          };
        });
        
        console.log("Created recharge contract options:", contractOptions);
        
        // Add default option and set to rechargeContractOptions
        rechargeContractOptions.value = contractOptions;
        rechargeContractSearchQuery.value = selectedValue || ''; // Set search query to selected value or empty

        // If we're editing and have a selectedValue, check if it's in the options
        if (selectedValue && contractOptions.length > 0 && !preventRecursion) {
          const found = contractOptions.some(option => option.value === selectedValue);
          if (!found) {
            // 在这里，如果没找到且不是通过searchRechargeContract调用的，
            // 那么我们直接添加一个临时选项，确保编辑时的值能够正确显示
            console.log("Selected recharge contract not found in results, adding temporary option:", selectedValue);
            rechargeContractOptions.value.push({
              label: `${selectedValue} (未找到详细信息)`,
              value: selectedValue,
              contract_amount: '' // 临时选项没有合同金额信息
            });
            // 确保选中值存在
            form.value.recharge_contract = selectedValue;
          } else {
            console.log("Selected recharge contract found in options:", selectedValue);
          }
        } else if (contractOptions.length === 1 && !form.value.recharge_contract && !selectedValue && !isInitialFormLoad.value) {
          // 只有在非初始加载阶段，并且只有一个选项，且当前没有选中值，且不是搜索模式，才自动选择第一个
          console.log("Auto-selecting the only available recharge contract option");
          form.value.recharge_contract = contractOptions[0].value;
        }
        
        // 如果是在初始加载阶段，确保之前的值被恢复
        if (isInitialFormLoad.value && currentValue) {
          console.log("Restoring recharge contract value after loading options:", currentValue);
          form.value.recharge_contract = currentValue;
        }
      }
    } else {
      // If API fails or returns no data, reset options
      rechargeContractOptions.value = [];
      rechargeContractSearchQuery.value = ''; // Reset search query
      
      // 即使API失败，在初始加载阶段也要恢复之前的值
      if (isInitialFormLoad.value && currentValue) {
        console.log("Restoring recharge contract value after API failure:", currentValue);
        form.value.recharge_contract = currentValue;
      }
      
      if (res && res.msg && res.msg !== "success") {
        console.warn("Failed to load recharge contracts:", res.msg);
        ElMessage.warning(`获取充值合同失败: ${res.msg}`);
      }
    }
    
    rechargeContractLoading.value = false;
  }).catch(err => {
    console.error("获取充值合同列表失败:", err);
    ElMessage.error("获取充值合同列表失败，请稍后重试");
    rechargeContractOptions.value = [];
    rechargeContractSearchQuery.value = ''; // Reset search query
    
    // 即使API出错，在初始加载阶段也要恢复之前的值
    if (isInitialFormLoad.value && currentValue) {
      console.log("Restoring recharge contract value after API error:", currentValue);
      form.value.recharge_contract = currentValue;
    }
    
    rechargeContractLoading.value = false;
  });
};

watch(() => form.value.platform_type, (newValue) => {
  const platformTypeNum = Number(newValue || 0);

  if (!isRechargeContractRequired()) {
    form.value.recharge_contract = "";
  } else if (newValue && form.value.contract_id) {
    // 只有当已经选择了客户合同时才加载充值合同
    loadRechargeContracts(form.value.contract_id, form.value.recharge_contract, false);
  }
});

watch(() => projectAddDrawer.value, (newValue) => {
  if (newValue) {
    console.log("Drawer opened");
    // No longer automatically load recharge contracts on drawer open
    // They will be loaded on demand when the user focuses on the field
  }
});

watch(() => form.value.project_id, (newProjectId, oldProjectId) => {
  // 在初始加载时跳过这个watcher
  if (isInitialFormLoad.value) {
    console.log("Skipping project_id watcher during initial load");
    return;
  }
  
  if (newProjectId && newProjectId !== oldProjectId) {
    // Reset form fields related to project
    form.value.customer_contract = "";
    form.value.contract_id = "";
    form.value.oa_project_id = "";
    oaProjectSource.value = '未知';
    form.value.recharge_contract = "";
    rechargeContractOptions.value = [];
    form.value.order_method = "";
    form.value.account_id = "";
    form.value.account_name = "";
    form.value.account_brand_name = "";

    const selectedProject = options.value.find(project => project.id === newProjectId);
    if (selectedProject) {
      // Set platform_type based on the selected project's project_platform field
      form.value.platform_type = selectedProject.project_platform || 1;
      
      // Reset task_type when project changes for Douyin platform
      if (Number(form.value.platform_type) === 1) {
        form.value.task_type = "";
      }

      // Update platform_type_entity based on platform type
      const platformTypeNum = Number(form.value.platform_type);
      switch(platformTypeNum) {
        case 1:
          form.value.platform_type_entity = "武汉星图新视界科技有限公司";
          break;
        case 2:
          form.value.platform_type_entity = "武汉星图新视界科技有限公司";
          break;
        case 3:
          form.value.platform_type_entity = "薯鸿文化传媒（上海）有限公司";
          break;
        case 4:
          form.value.platform_type_entity = "北京晨钟科技有限公司";
          break;
        case 5:
          form.value.platform_type_entity = "上海东方传媒（集团）有限公司‌‌";
          break;
        case 6:
          form.value.platform_type_entity = "深圳市腾讯文化传媒有限公司";
          break;
        default:
          form.value.platform_type_entity = "";
      }

      // Update platformLabel to display the correct media platform
      const platformOption = mediaPlatformOptions.value.find(item => item.value === form.value.platform_type);
      platformLabel.value = platformOption ? platformOption.label : '';

      form.value.customer_entity = selectedProject.custom_name || "";
      if (!selectedProject.custom_name) {
        ElMessage.warning('所选项目缺少客户主体信息，请选择包含完整信息的项目');
      }

      // Load contract information if USC code is available
      if (selectedProject.usc_code) {
        contractLoading.value = true;
        contractInformationListFun(selectedProject.usc_code, "");
      }
    }
  }
});

const oaProjectSource = ref('未知');

const filteredOrderMethodOptions = computed(() => {
  const selectedProject = options.value.find(project => project.id === form.value.project_id);
  const isStarCubeProject = selectedProject && selectedProject.promote_type === 1;
  const platformType = Number(form.value.platform_type);
  const taskMode = Number(form.value.task_type);

  // 当选择B站平台时，只能线下下单
  const isBilibili = platformType === 5;

  return orderMethodOptions.value.map(option => {
    if (option.value === "1") {
      // "星推下单" 的禁用条件:
      // 1. When platform_type is not 1 (抖音)
      // 2. When platform_type is 1 (抖音) AND task_type is 2(招募) or 3(投稿)
      // 3. When platform_type is 5 (B站)
      return {
        ...option,
        disabled:
          platformType !== 1 ||
          (platformType === 1 && [2, 3].includes(taskMode)) ||
          isBilibili
      };
    } else if (option.value === "2") {
      // "集采线下下单" 的禁用条件:
      // 当选择星立方+指派时，禁用集采线下下单
      // B站平台必须使用线下下单，不禁用
      return {
        ...option,
        disabled: isStarCubeProject && platformType === 1 && taskMode === 1 && !isBilibili
      };
    }
    return { ...option, disabled: false };
  });
});

// 客户主体修改弹窗的可见性状态
const customerEntityDialogVisible = ref(false);

// 打开客户主体修改弹窗
const openEditCustomerEntityDialog = () => {
  // 将当前表单中的客户主体值赋给对话框表单
  customerEntityForm.value.customer_entity = form.value.customer_entity || "";
  // 打开对话框前先加载客户主体选项
  loadCustomerEntityOptions();
  customerEntityDialogVisible.value = true;
};

// 保存修改后的客户主体值
const saveCustomerEntity = () => {
  // 检查选择是否为空
  if (!customerEntityForm.value.customer_entity) {
    ElMessage.warning("请选择客户主体");
    return;
  }

  // 将选择的值赋回主表单
  form.value.customer_entity = customerEntityForm.value.customer_entity;

  // 如果有其他客户信息字段，也可以保存
  if (form.value.hasOwnProperty('custom_id') && customerEntityForm.value.custom_id) {
    form.value.custom_id = customerEntityForm.value.custom_id;
  }

  if (form.value.hasOwnProperty('usc_code') && customerEntityForm.value.usc_code) {
    form.value.usc_code = customerEntityForm.value.usc_code;
  }

  // 客户主体改变后，清空相关的合同信息并重新加载合同列表
  form.value.contract_id = "";
  form.value.customer_contract = "";
  customerUscList.value = [];
  isCurrentObj.value = null; // 清空当前选中的合同对象

  // 同时清空充值合同相关信息，因为充值合同依赖于客户合同
  form.value.recharge_contract = "";
  rechargeContractOptions.value = [];

  // 使用新的客户主体的usc_code重新加载合同列表
  if (customerEntityForm.value.usc_code) {
    contractLoading.value = true;
    contractInformationListFun(customerEntityForm.value.usc_code, "");
  }

  // 关闭对话框
  customerEntityDialogVisible.value = false;

  // 提示用户修改成功
  ElMessage.success("客户主体已更新，合同列表已刷新");
};

// 账户选项数据
const accountOptions = ref([]);
const accountLoading = ref(false);


// 监听下单方式变化，当不是集采线下下单时清空账户信息
watch(() => form.value.order_method, (newValue) => {
  // 如果不是集采线下下单(值为2)，清空账户信息
  if (newValue !== '2') {
    form.value.account_id = "";
    form.value.account_name = "";
  }
});

// 客户主体修改表单数据
const customerEntityForm = ref({
  customer_entity: "",
  custom_id: undefined,
  custom_company: "",
  cust_abbr: "",
  usc_code: ""
});

// 账户选择弹窗相关
const accountDialogVisible = ref(false);
const accountTable = ref(null);
const selectedAccount = ref(null); // 当前选中的账户
const savedSelectedAccount = ref(null); // 保存最后一次确认选择的账户，用于tooltip显示
const selectedAccountId = ref(''); // 用于绑定radio选择器的值
const accountSearchForm = ref({
  customer_account_id: '', // 账户ID
  account_name: '', // 账户名称
  customer_name: '', // 客户名称
  media_platform: '', // 媒体平台
});
const accountPagination = ref({
  currentPage: 1,
  pageSize: 10,
  total: 0
});

// 打开账户选择弹窗
const openAccountDialog = () => {
  accountDialogVisible.value = true;
  // 重置已选择的账户
  selectedAccount.value = null;
  selectedAccountId.value = ''; // 清空单选按钮的选中状态
  
  // 如果已有选择的账户ID，在账户列表中选中它
  if (form.value.account_id) {
    // 加载账户列表以便标记当前选中的账户
    searchAccounts();
    // 在数据加载后设置选中状态
    selectedAccountId.value = form.value.account_id;
  } else {
    // 如果没有选择，重置并加载第一页数据
    resetAccountSearch();
  }
};

// 重置账户搜索条件
const resetAccountSearch = () => {
  accountSearchForm.value = {
    customer_account_id: '',
    account_name: '',
    customer_name: '',
    media_platform: '',
  };
  accountPagination.value.currentPage = 1;
  selectedAccountId.value = ''; // 清空单选按钮的选中状态
  selectedAccount.value = null; // 清空当前选择的账户
  searchAccounts();
};

// 搜索账户
const searchAccounts = () => {
  accountLoading.value = true;
  
  // 准备搜索参数
  const params = {
    account_name: accountSearchForm.value.account_name || '',
    customer_name: accountSearchForm.value.customer_name || '',
    customer_account_id: accountSearchForm.value.customer_account_id || '',
    media_platform: accountSearchForm.value.media_platform || '',
    page: accountPagination.value.currentPage,
    page_size: accountPagination.value.pageSize
  };
  
  // 使用自定义账户列表API，传入搜索参数
  getRechargeInfo(params).then(res => {
    accountLoading.value = false;
    if (res && res.code === 990 && res.data) {
      accountOptions.value = res.data.list || [];
      accountPagination.value.total = res.data.count || 0;
      
      // 如果有已选择的账户ID，在表格中选中对应行
      if (selectedAccountId.value) {
        const matchedAccount = accountOptions.value.find(
          account => account.id === selectedAccountId.value
        );
        
        if (matchedAccount && accountTable.value) {
          // 设置当前行
          accountTable.value.setCurrentRow(matchedAccount);
          selectedAccount.value = matchedAccount;
        }
      }
    } else {
      accountOptions.value = [];
      accountPagination.value.total = 0;
      if (res && res.msg) {
        ElMessage.warning(res.msg);
      }
    }
  }).catch(err => {
    accountLoading.value = false;
    console.error("获取账户列表失败:", err);
    ElMessage.error("获取账户列表失败");
    accountOptions.value = [];
    accountPagination.value.total = 0;
  });
};

// 处理分页大小变化
const handleAccountSizeChange = (size) => {
  accountPagination.value.pageSize = size;
  searchAccounts();
};

// 处理分页页码变化
const handleAccountPageChange = (page) => {
  accountPagination.value.currentPage = page;
  searchAccounts();
};

// 处理表格行选择变化
const handleAccountRowChange = (row) => {
  selectedAccount.value = row;
  if (row) {
    selectedAccountId.value = row.id;
  }
};

// 处理单选按钮变化
const handleRadioChange = (row) => {
  selectedAccount.value = row;
  // 如果使用了单选按钮，也要确保表格的当前行设置正确
  if (accountTable.value) {
    accountTable.value.setCurrentRow(row);
  }
};

// 确认选择账户
const confirmSelectAccount = () => {
  if (!selectedAccount.value) {
    ElMessage.warning('请选择一个账户');
    return;
  }
  
  // 更新表单中的账户信息
  form.value.account_id = selectedAccount.value.id;
  
  // 格式化账户显示信息，包含账户ID、账户名称和品牌名称
  const accountInfo = selectedAccount.value;
  // 格式: "账户ID | 账户名称 | 品牌名称"
  const formattedAccountInfo = `${accountInfo.customer_account_id} | ${accountInfo.account_name}${accountInfo.brand_name ? ' | ' + accountInfo.brand_name : ''}`;
  form.value.account_name = formattedAccountInfo;
  
  // 保存账户品牌名称
  form.value.account_brand_name = accountInfo.brand_name || '';
  
  // 保存选中的账户信息，用于后续的tooltip显示
  savedSelectedAccount.value = JSON.parse(JSON.stringify(selectedAccount.value));
  
  // 关闭弹窗
  accountDialogVisible.value = false;
  
  // 提示用户已选择账户
  ElMessage.success(`已选择账户: ${accountInfo.account_name}`);
};

// 打开客户主体修改弹窗
const customerEntityRemoteMethod = query => {
  if (query) {
    customerEntityLoading.value = true;
    let params = {
      search: query,
      page: 1,
      page_size: 10
    };
    customerProfile(params).then(res => {
      customerEntityLoading.value = false;
      if (res.data?.lists) {
        customerEntityOptions.value = res.data.lists;
      }
    });
  } else {
    loadCustomerEntityOptions();
  }
};

// 加载默认的客户主体选项
const loadCustomerEntityOptions = () => {
  customerEntityLoading.value = true;
  let params = {
    search: "",
    page: 1,
    page_size: 10
  };
  customerProfile(params).then(res => {
    customerEntityLoading.value = false;
    if (res.data?.lists) {
      customerEntityOptions.value = res.data.lists;
    }
  });
};

// 为客户主体下拉框添加的变量和方法
const customerEntityOptions = ref([]);
const customerEntityLoading = ref(false);

// 处理客户主体选择变更
const handleEntitySelectChange = value => {
  if (customerEntityOptions.value.length) {
    const selectedCustomer = customerEntityOptions.value.find(e => value === e.custom_name);
    if (selectedCustomer) {
      customerEntityForm.value.customer_entity = selectedCustomer.custom_name;
      
      // 存储客户相关的其他信息，以备需要
      customerEntityForm.value.custom_id = selectedCustomer.custom_id;
      customerEntityForm.value.custom_company = selectedCustomer.custom_company || "";
      customerEntityForm.value.cust_abbr = selectedCustomer.cust_abbr || "";
      customerEntityForm.value.usc_code = selectedCustomer.usc_code || "";
    }
  }
};

const findReviewerByLevel = (reviewers, level) => {
  if (!reviewers || !reviewers.length) return null;
  const matchingReviewers = reviewers.filter(r => r.reviewer_level === level);
  return matchingReviewers.length > 0 ? matchingReviewers[0] : null;
};

const getAccountTooltipContent = () => {
  // 优先使用保存的选中账户数据
  const accountData = savedSelectedAccount.value;
  
  if (accountData) {
    return `
      <div style="text-align: left;">
        <div><strong>所属公司:</strong> ${accountData.company || '-'}</div>
        <div><strong>媒体平台:</strong> ${accountData.media_platform || '-'}</div>
        <div><strong>客户名称:</strong> ${accountData.customer_name || '-'}</div>
        <div><strong>账户ID:</strong> ${accountData.customer_account_id || '-'}</div>
        <div><strong>账户名称:</strong> ${accountData.account_name || '-'}</div>
        <div><strong>品牌名称:</strong> ${accountData.brand_name || '-'}</div>
      </div>
    `;
  }
  
  // 如果找不到账户对象，回退到使用显示的账户名称
  return `<div>账户信息: ${form.value.account_name || '-'}</div>`;
};

// 用于调试任务过滤的帮助函数
const debugTask = () => {
  const taskId = route.query.task_id;
  if (taskId) {
    console.log("%c[TASK DEBUGGER]", "background:orange;color:white;padding:2px 5px;border-radius:3px", "Found task_id in URL:", taskId);
    
    // 将 task_id 存储到 sessionStorage，确保后续调用能找到它
    const taskIdStr = Array.isArray(taskId) ? taskId[0] : String(taskId);
    sessionStorage.setItem("task_search_debug", taskIdStr);
    
    // 尝试多种可能的参数名称
    const testParams1 = {
      task_search: taskIdStr,
      page: 1,
      page_size: 10
    };
    
    const testParams2 = {
      id: taskIdStr,
      page: 1,
      page_size: 10
    };
    
    const testParams3 = {
      task_id: taskIdStr,
      page: 1,
      page_size: 10
    };
    
    console.log("%c[TASK DEBUGGER]", "background:orange;color:white;padding:2px 5px;border-radius:3px", "Testing API with different parameters");
    
    // 测试第一种参数组合
    console.log("%c[TASK DEBUGGER]", "background:orange;color:white;padding:2px 5px;border-radius:3px", "Test 1 - task_search:", testParams1);
    searchTasks(testParams1).then(res => {
      console.log("%c[TASK DEBUGGER]", "background:orange;color:white;padding:2px 5px;border-radius:3px", "API test 1 result:", res);
      checkTaskInResults(res, taskIdStr, "Test 1");
      
      // 测试第二种参数组合
      console.log("%c[TASK DEBUGGER]", "background:orange;color:white;padding:2px 5px;border-radius:3px", "Test 2 - id:", testParams2);
      return searchTasks(testParams2);
    }).then(res => {
      console.log("%c[TASK DEBUGGER]", "background:orange;color:white;padding:2px 5px;border-radius:3px", "API test 2 result:", res);
      checkTaskInResults(res, taskIdStr, "Test 2");
      
      // 测试第三种参数组合
      console.log("%c[TASK DEBUGGER]", "background:orange;color:white;padding:2px 5px;border-radius:3px", "Test 3 - task_id:", testParams3);
      return searchTasks(testParams3);
    }).then(res => {
      console.log("%c[TASK DEBUGGER]", "background:orange;color:white;padding:2px 5px;border-radius:3px", "API test 3 result:", res);
      checkTaskInResults(res, taskIdStr, "Test 3");
    }).catch(err => {
      console.log("%c[TASK DEBUGGER]", "background:red;color:white;padding:2px 5px;border-radius:3px", "API test error:", err);
    });
  }
};

// 检查任务是否在结果中的辅助函数
const checkTaskInResults = (res, taskIdStr, testName) => {
  if (res?.data?.tasks) {
    const found = res.data.tasks.some(task => String(task.id) === taskIdStr);
    console.log("%c[TASK DEBUGGER]", "background:orange;color:white;padding:2px 5px;border-radius:3px", 
      `${testName}: Task ${taskIdStr} found in results: ${found ? 'YES' : 'NO'}`);
      
    if (found) {
      console.log("%c[TASK DEBUGGER]", "background:green;color:white;padding:2px 5px;border-radius:3px", 
        `${testName}: Found task in API results, should be visible in UI`);
    } else {
      console.log("%c[TASK DEBUGGER]", "background:red;color:white;padding:2px 5px;border-radius:3px", 
        `${testName}: Task not found in API results. Check if task ID exists in the system.`);
    }
  } else {
    console.log("%c[TASK DEBUGGER]", "background:red;color:white;padding:2px 5px;border-radius:3px", 
      `${testName}: API did not return task data.`);
  }
};

// Add this variable before onMounted
const isFirstLoad = ref(true);

const getRechargeContractPlaceholder = () => {
  // If no contract is selected yet, indicate that a contract must be selected first
  if (!form.value.contract_id) {
    return "请先选择客户合同";
  }

  // If the platform doesn't require a recharge contract, indicate that
  if (!isRechargeContractRequired()) {
    return "当前媒体平台不需要选择充值合同";
  }

  // Otherwise, provide a platform-specific message
  const platformType = Number(form.value.platform_type);

  if (platformType === 2) {
    return "请选择抖音星立方的充值合同";
  } else if (platformType === 3) {
    return "请选择小红书的充值合同";
  } else if (platformType === 5) {
    return "请选择B站的充值合同";
  } else {
    return "请点击选择充值合同";
  }
};

const formatProjectName = (project) => {
  if (!project) return '';
  
  const platformType = project.project_platform || 1;
  const promoteType = project.promote_type==1?'星立方':'其他';
  const platformLabel = mediaPlatformOptions.value.find(item => item.value === platformType)?.label || '';
  if (platformType === 1) {
    return `${platformLabel}-${promoteType}-${project.project_name}`;
  } else {
    return `${platformLabel}-${project.project_name}`;
  }
};

watch(() => form.value.project_id, (newProjectId, oldProjectId) => {
  // 在初始加载时跳过这个watcher
  if (isInitialFormLoad.value) {
    console.log("Skipping project_id watcher during initial load");
    return;
  }
  
  if (newProjectId && newProjectId !== oldProjectId) {
    // Reset form fields related to project
    form.value.customer_contract = "";
    form.value.contract_id = "";
    form.value.oa_project_id = "";
    oaProjectSource.value = '未知';
    form.value.recharge_contract = "";
    rechargeContractOptions.value = [];
    form.value.order_method = "";
    form.value.account_id = "";
    form.value.account_name = "";
    form.value.account_brand_name = "";

    const selectedProject = options.value.find(project => project.id === newProjectId);
    if (selectedProject) {
      // Set platform_type based on the selected project's project_platform field
      form.value.platform_type = selectedProject.project_platform || 1;
      
      // Reset task_type when project changes for Douyin platform
      if (Number(form.value.platform_type) === 1) {
        form.value.task_type = "";
      }

      // Update platform_type_entity based on platform type
      const platformTypeNum = Number(form.value.platform_type);
      switch(platformTypeNum) {
        case 1:
          form.value.platform_type_entity = "武汉星图新视界科技有限公司";
          break;
        case 2:
          form.value.platform_type_entity = "武汉星图新视界科技有限公司";
          break;
        case 3:
          form.value.platform_type_entity = "薯鸿文化传媒（上海）有限公司";
          break;
        case 4:
          form.value.platform_type_entity = "北京晨钟科技有限公司";
          break;
        case 5:
          form.value.platform_type_entity = "上海东方传媒（集团）有限公司‌‌";
          break;
        case 6:
          form.value.platform_type_entity = "深圳市腾讯文化传媒有限公司";
          break;
        default:
          form.value.platform_type_entity = "";
      }

      // Update platformLabel to display the correct media platform
      const platformOption = mediaPlatformOptions.value.find(item => item.value === form.value.platform_type);
      platformLabel.value = platformOption ? platformOption.label : '';

      form.value.customer_entity = selectedProject.custom_name || "";
      form.value.usc_code = selectedProject.usc_code || ""; // 更新表单中的USC代码
      if (!selectedProject.custom_name) {
        ElMessage.warning('所选项目缺少客户主体信息，请选择包含完整信息的项目');
      }

      // Load contract information if USC code is available
      if (selectedProject.usc_code) {
        contractLoading.value = true;
        contractInformationListFun(selectedProject.usc_code, "");
      }
    }
  }
});

const platformLabel = ref("");

const getTaskModeOptions = () => {
  const platformType = Number(form.value.platform_type);
  const selectedProject = options.value.find(project => project.id === form.value.project_id);
  if (platformType === 1) { // 抖音平台
    const isStarCubeProject = selectedProject && selectedProject.promote_type === 1;
    if (isStarCubeProject) {
      return taskModeOptions.value.douyin.map(option => ({
        ...option,
        disabled: option.value !== "1" // Only enable "指派" (value 1)
      }));
    } else {
      return taskModeOptions.value.douyin;
    }
  } else if (platformType === 5) { // Bilibili
    return taskModeOptions.value.bilibili;
  }

  return [];
};

watch(() => form.value.platform_type, (newValue, oldValue) => {
  // 在初始加载时跳过这个watcher
  if (isInitialFormLoad.value) {
    console.log("Skipping platform_type watcher during initial load");
    return;
  }

  const platformTypeNum = Number(newValue || 0);

  // Update platformLabel to display the current platform name
  const platformOption = mediaPlatformOptions.value.find(item => item.value === platformTypeNum);
  platformLabel.value = platformOption ? platformOption.label : '';

  // Update platform_type_entity based on platform type
  switch(platformTypeNum) {
    case 1:
      form.value.platform_type_entity = "武汉星图新视界科技有限公司";
      break;
    case 2:
      form.value.platform_type_entity = "武汉星图新视界科技有限公司";
      break;
    case 3:
      form.value.platform_type_entity = "薯鸿文化传媒（上海）有限公司";
      break;
    case 4:
      form.value.platform_type_entity = "北京晨钟科技有限公司";
      break;
    case 5:
      form.value.platform_type_entity = "上海东方传媒（集团）有限公司‌‌";
      break;
    case 6:
      form.value.platform_type_entity = "深圳市腾讯文化传媒有限公司";
      break;
    default:
      form.value.platform_type_entity = "";
  }

  // 修正条件判断逻辑：先检查是否需要充值合同
  if (isRechargeContractRequired()) {
    // 如果需要充值合同，并且已选择了客户合同，则加载充值合同
    if (form.value.contract_id) {
      console.log("Platform type changed, loading recharge contracts for current contract");
      loadRechargeContracts(form.value.contract_id, form.value.recharge_contract, false);
    }
  } else {
    // 如果不需要充值合同，清空充值合同值
    form.value.recharge_contract = "";
    rechargeContractOptions.value = [];
  }
});

// 修复task_type的watch函数，添加对isInitialFormLoad的检查
watch(() => form.value.task_type, (newValue) => {
  // 在初始加载时跳过这个watcher
  if (isInitialFormLoad.value) {
    return;
  }

  const selectedProject = options.value.find(project => project.id === form.value.project_id);
  const isStarCubeProject = selectedProject && selectedProject.promote_type === 1;
  const platformType = Number(form.value.platform_type);
  const taskMode = Number(newValue);

  if (platformType === 1 && [2, 3].includes(taskMode)) {
    // 抖音平台且任务模式为招募或投稿时，自动选择集采线下下单
    form.value.order_method = '2';
  } else if (isStarCubeProject && platformType === 1 && taskMode === 1) {
    // 星立方项目且任务模式为指派时，自动选择星推下单
    form.value.order_method = '1';
  }
});

const shouldShowTaskType = (row) => {
  const platformType = Number(row.platform_type);
  // Only show task type for platforms 1 (抖音) and 5 (B站)
  return platformType === 1 || platformType === 5;
};

// 判断是否显示结算方式字段
const shouldShowSettlementMethod = () => {
  const platformType = Number(form.value.platform_type);
  const taskType = Number(form.value.task_type);
  // 只有抖音平台（platform_type === 1）且任务模式为指派（task_type === 1）时才显示
  return platformType === 1 && taskType === 1;
};

// 判断是否显示结算方式列（在列表中）
const shouldShowSettlementMethodColumn = computed(() => {
  // 检查当前列表中是否有任何抖音指派任务
  return tableData.value.some(row => shouldShowSettlementMethodForRow(row));
});

// 判断某一行是否应该显示结算方式
const shouldShowSettlementMethodForRow = (row) => {
  const platformType = Number(row.platform_type);
  const taskType = Number(row.task_type);
  // 只有抖音平台（platform_type === 1）且任务模式为指派（task_type === 1）时才显示
  return platformType === 1 && (taskType === 1 || taskType === 2);
};

// 获取结算方式标签
const getSettlementMethodLabel = (settlementMethod) => {
  const settlementMethodMap = {
    1: "一口价",
    3: "按照自然播放量结算",
    4: "按转化结算"
  };
  return settlementMethodMap[settlementMethod] || "-";
};

const getTaskTypeLabel = (row) => {
  if (!row.task_type) return "";

  const platformType = Number(row.platform_type);
  const taskTypeValue = Number(row.task_type);

  // 抖音平台任务类型
  if (platformType === 1) {
    switch (taskTypeValue) {
      case 1: return "指派";
      case 2: return "招募";
      case 3: return "投稿";
      default: return "";
    }
  }

  // B站平台任务类型
  if (platformType === 5) {
    switch (taskTypeValue) {
      case 1: return "京火";
      case 2: return "花火";
      default: return "";
    }
  }

  // 其他平台不显示任务类型，但如果调用到这里，返回通用标签
  return task_type[taskTypeValue] || "";
};

// 判断是否为抖音平台招募任务
const isDouyinRecruitmentTask = (row) => {
  const platformType = Number(row.platform_type);
  const taskTypeValue = Number(row.task_type);

  // 抖音平台（platform_type === 1）且招募任务（task_type === 2）
  return platformType === 1 && taskTypeValue === 2;
};

// 添加处理充值合同选择框获得焦点时的事件处理函数
const handleRechargeContractFocus = () => {
  // 检查是否需要充值合同
  if (!isRechargeContractRequired()) {
    ElMessage.info('当前媒体平台不需要选择充值合同');
    return;
  }

  // Check if client contract is selected
  if (!form.value.contract_id) {
    ElMessage.warning('请先选择客户合同，然后才能选择充值合同');
    return;
  }
  
  console.log("Loading recharge contracts on focus for contract ID:", form.value.contract_id);
  
  // Reset debounce state to force a fresh API call
  if (rechargeContractDebounceTimer) {
    clearTimeout(rechargeContractDebounceTimer);
    rechargeContractDebounceTimer = null;
  }
  lastRechargeContractRequest = 0;
  
  // Always load options when focusing on the field, regardless of previous calls
  loadRechargeContracts(form.value.contract_id, "", true);
  
  // Reset search query when focusing
  rechargeContractSearchQuery.value = '';
};

</script>
<style>
.popperClass {
  box-shadow: 0 2px 0 rgba(0, 0, 0, 0.045);
}

.el-submenu .el-menu-item {
  min-width: 8rem !important;
}
</style>
<style lang="scss" scoped>
.m0 {
  margin: 0px;
}
.table-box{
  height: 100%;
}
.table-box .table-search {
  background-color: #fff !important;
}
.avatar-stack {
  display: flex;
  align-items: center;
  margin-left: 5px;
  .avatar {
    margin-left: -5px;
  }
  .number-avatar {
    height: 24px;
    width: 24px;
    line-height: 24px;
    font-size: 10px;
    text-align: center;
    border-radius: 50%;
    background-color: #e5e5e5;
    color: #666;
    margin-left: -5px;
  }
}

/* 新增审核人员名单样式 */
.show-more,
.show-less {
  margin-left: 5px;
  color: #409EFF;
  cursor: pointer;
  font-size: 12px;
}

.show-more:hover,
.show-less:hover {
  text-decoration: underline;
}

.reviewer-names {
  display: inline-block;
  max-width: 300px;
  white-space: normal;
  word-break: break-word;
  line-height: 1.4;
  vertical-align: top; /* Align text properly */
}

.reviewer-list {
  margin-top: 6px;
  padding: 8px;
  max-height: 150px;
  max-width: 320px;
  overflow-y: auto;
  background-color: #f7f7f7;
  border-radius: 4px;
  font-size: 13px;
  line-height: 1.4;
  white-space: normal;
  word-break: break-word;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

/* 自定义滚动条样式 */
.reviewer-list::-webkit-scrollbar {
  width: 6px;
}

.reviewer-list::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.reviewer-list::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.reviewer-list::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* 让tooltip更宽一些以容纳更多内容，但固定最大宽度 */
:deep(.el-tooltip__popper) {
  max-width: 360px !important;
  min-width: 280px;
  width: auto !important;
  overflow: hidden;
}

.task-detail-tabs-filter-item {
  margin-bottom: 10px;
}

.request_box {
  font-size: 14px;

  margin: 0px auto 1px;
  position: relative;
  padding: 1px 10px;
  border-radius: 5px;
  background: #ebebeb;
  display: flex;
  justify-content: space-between;
  margin-bottom: 15px;
}

.request_box_left {
  flex-basis: 94%;
  padding-right: 20px;
}

.request_box_right {
  width: 130px;
  flex-wrap: wrap;
  flex-direction: row;
  justify-content: flex-start;
}

.cpmcpe {
  border: 1px solid #000;
  border-radius: 5px;
  display: flex;
  /* 方向设置为竖轴 */
  flex-direction: column;
  line-height: 14px;
  padding: 10px 15px;
  margin-right: 10px;
}

.cpmcpe div:nth-child(1) {
  margin-bottom: 10px;
}

.cpmcpe div:nth-child(2) {
  font-size: 12px;
  color: #999;
}

/* 抽屉容器 */
.drawer-container {
  height: 100%;
  overflow-y: auto;
  padding: 0 10px;
}

/* 表单基础样式 */
.form-style {
  height: 100%;
}

/* 表单容器样式 */
.form-container {
  padding: 10px 5px;
}

/* 重置行间距，避免挤压 */
:deep(.el-row) {
  margin-bottom: 0 !important;
}

/* 表单项更均匀的间距 */
:deep(.el-form-item) {
  margin-bottom: 22px;
  position: relative;
}

:deep(.el-form-item__error) {
  position: absolute;
  top: 100%;
  left: 0;
}

/* 带图标的表单标签 */
.form-item-label-with-icon {
  display: flex;
  align-items: center;
  
  .question-icon {
    margin-left: 4px;
    font-size: 16px;
    color: #909399;
    cursor: help;
  }
}

/* 带操作按钮的输入框 */
.form-input-with-action {
  display: flex;
  align-items: center;
  width: 100%;
  
  .form-action {
    margin-left: 10px;
    white-space: nowrap;
  }
}

/* 合同选项样式 */
.contract-option {
  padding: 5px 0;
  
  .contract-option-title {
    font-size: 14px;
    margin-bottom: 4px;
  }
  
  .contract-option-id {
    font-size: 12px;
    color: #999;
    margin-bottom: 2px;
  }
  
  .contract-option-project {
    font-size: 12px;
    color: #409EFF;
    
    .contract-option-note {
      color: #F56C6C;
      margin-left: 5px;
    }
  }
}

/* 合同详情样式 */
.contract-details {
  margin-top: 12px;
  padding: 10px;
  background-color: #f9f9f9;
  border-radius: 4px;
  border-left: 3px solid #409EFF;
  font-size: 13px;
}

.contract-details-item {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
  flex-wrap: wrap;
  
  &:last-child {
    margin-bottom: 0;
  }
  
  .contract-label {
    font-weight: 500;
    color: #606266;
    margin-right: 8px;
    min-width: 90px;
  }
  
  .expire-date {
    color: #F56C6C;
  }
  
  :deep(.el-tag) {
    margin-left: 8px;
  }
}

.contract-dates {
  background-color: #f0f9ff;
  padding: 5px 8px;
  border-radius: 3px;
  margin: 5px 0;
}

/* 字段提示信息 */
.field-note {
  margin-top: 8px;
  line-height: 1.4;
}

/* 抽屉底部操作区 */
.drawer-footer {
  display: flex;
  justify-content: flex-end;
  padding: 10px 20px;
  border-top: 1px solid #ebeef5;
  background-color: #fff;
  
  .el-button + .el-button {
    margin-left: 12px;
  }
}

.form-span {
  margin-top: 20px;
  display: block;
  color: var(--el-color-primary);
}

.content {
  background: var(--el-bg-color);
  width: 100%;
}

.content_box {
  padding: 10px 15px;
  border-radius: 5px;
  background: var(--el-bg-color);
  height: calc(100% - 20px);
}

.el-tab-pane {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.pagina_box {
  height: 90px;
  margin-top: -20px;
  margin-bottom: 15px;
  display: flex;
  justify-content: center;
}

.comName {
  font-size: 14px;
  font-weight: normal !important;
}

:deep(.el-range-editor.el-input__inner) {
  display: -webkit-inline-box;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  padding: 3px 10px;
  height: 32px;
}
/*
:deep(.el-radio__input) {
  display: none;
} */
.task-detail-tabs-filter {
  :deep(.el-checkbox__input) {
    display: none;
  }
}

:deep(.el-date-editor .el-range-separator) {
  height: 100%;
  padding: 0 6px;
  margin: 0;
  text-align: center;
  font-size: 14px;
  width: 10%;
  color: var(--el-text-color-primary);
}

:deep(.el-input--suffix .el-input__inner) {
  padding-right: 30px;
  height: 32px;
}

.el-menu {
  height: 87vh;
}

.arrow-span {
  background: var(--el-bg-color);
  width: 30px;
  height: 30px;
  border-radius: 50%;
  display: inline-block;
  position: absolute;
  right: -10px;
  top: 50%;
  z-index: 999;
  text-align: center;
  line-height: 32px;
  background-color: #ffffff;
  box-shadow:
    0 2px 8px -2px rgba(0, 0, 0, 0.05),
    0 1px 4px -1px rgba(25, 15, 15, 0.07),
    0 0 1px 0 rgba(0, 0, 0, 0.08);
  transition: transform 0.3s;
  cursor: pointer;
}

.el-menu-item:hover {
  background-color: rgb(170, 63, 200, 0.1);
}

.el-menu-item.is-active {
  color: rgb(170, 63, 200);
  background: rgb(170, 63, 200, 0.1) !important;
}

.request_box_left {
  padding-left: 10px;
  padding-top: 7px;
}

:deep(.el-drawer__body) {
  padding: 0;
  overflow: hidden;
}

:deep(.form-btn .el-form-item__content) {
  justify-content: flex-end;
}

.table-main {
  display: flex;
  flex: 1;
  flex-direction: column;
  width: 100%;
  height: 100%;
}

.el-tabs--top {
  height: 100%;
}

/* 账户选择对话框样式 */
.account-search-form {
  margin-bottom: 20px;
  padding: 15px;
  background-color: #f8f8f8;
  border-radius: 4px;
}

.pagination-container {
  display: flex;
  justify-content: flex-end;
  margin-top: 15px;
}

/* 账户选择对话框样式 */
:deep(.el-radio) {
  margin-right: 0;
  .el-radio__label {
    display: none; /* 隐藏空白标签以减少占用空间 */
  }
  .el-radio__inner {
    margin-left: 8px; /* 调整单选按钮的位置，使其居中 */
  }
}

</style>
